CREATE TABLE [dbo].[ImportFileItem] (
    [IDImportFileItem] BIGINT          IDENTITY (1, 1) NOT NULL,
    [IDImportFile]     INT             NOT NULL,
    [ProductCode]      NVARCHAR (14)   NULL,
    [SerialNumber]     NVARCHAR (20)   NULL,
    [CodeText]         NVARCHAR (1024) NULL,
    [Imported]         BIT             CONSTRAINT [DF__ImportFil__Impor__2B0A656D] DEFAULT ((0)) NOT NULL,
    [IsError]          BIT             CONSTRAINT [DF__ImportFil__IsErr__2BFE89A6] DEFAULT ((0)) NOT NULL,
    [ImportStatus]     NVARCHAR (50)   NULL,
    CONSTRAINT [PK_ImportFileItem] PRIMARY KEY CLUSTERED ([IDImportFileItem] ASC),
    CONSTRAINT [FK_ImportFileItem_ImportFile] FOREIGN KEY ([IDImportFile]) REFERENCES [dbo].[ImportFile] ([IDImportFile])
);


GO

CREATE NONCLUSTERED INDEX [idxProductCodeStaus]
    ON [dbo].[ImportFileItem]([ProductCode] ASC, [Imported] ASC, [IsError] ASC, [ImportStatus] ASC);


GO

