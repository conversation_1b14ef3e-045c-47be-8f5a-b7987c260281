CREATE TABLE [dbo].[OrderProductPoolRequest] (
    [IDOrderProductPoolRequest] INT            IDENTITY (1, 1) NOT NULL,
    [IDOrderProduct]            INT            NOT NULL,
    [ReferenceID]               NVARCHAR (50)  NOT NULL,
    [RequestedQuantity]         INT            NOT NULL,
    [AllocatedQuantity]         INT            NOT NULL,
    [TimestampCreated]          DATETIME       CONSTRAINT [DF_OrderProductPool_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]               NVARCHAR (128) NOT NULL,
    [SNXIDOrderRequest]         INT            NULL,
    [IDSNXOrderRequest]         INT            NULL,
    CONSTRAINT [PK_OrderProductPool] PRIMARY KEY CLUSTERED ([IDOrderProductPoolRequest] ASC),
    CONSTRAINT [FK_OrderProductPool_OrderProduct] FOREIGN KEY ([IDOrderProduct]) REFERENCES [dbo].[OrderProduct] ([IDOrderProduct])
);


GO

