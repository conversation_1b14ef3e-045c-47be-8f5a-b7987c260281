 
  CREATE procedure [dbo].[sp_GetActivePoolsForProduct](@IDProduct int)  
as  
begin  
set nocount on  

 

declare @SQL nvarchar(max) =
N'select   
 p.IDPool, p.Code, p.[Name], Free = p.FreeGen, RequestedQty = 0     
from [SGCloudSNGenerator_PRD].[dbo].[Pool] p with (nolock)  
where   
 p.FreeGen > 0   
 and p.IDProduct = ('+ convert(nvarchar(100), @IDProduct)+')
 and p.SerializationType = ''RUSSIAN'' ';

 

EXEC(@SQL); -- For RuMDLP project IDProduct is  + 1 000 000
  
  
set nocount off  
  
end

GO

