CREATE TABLE [dbo].[AuditTrail] (
    [IDAuditTrail]            BIGINT         IDENTITY (1, 1) NOT NULL,
    [Action]                  NVARCHAR (100) NULL,
    [IDPool]                  INT            NULL,
    [IDRequest]               INT            NULL,
    [IDSerializationTemplate] INT            NULL,
    [TableName]               NVARCHAR (100) NULL,
    [FieldName]               NVARCHAR (100) NULL,
    [OldValue]                NVARCHAR (MAX) NULL,
    [NewValue]                NVARCHAR (MAX) NULL,
    [Timestamp]               DATETIME       CONSTRAINT [DF_AuditTrail_TimeStamp] DEFAULT (getdate()) NULL,
    [User]                    NVARCHAR (450) NULL,
    CONSTRAINT [PK_AuditTrail] PRIMARY KEY CLUSTERED ([IDAuditTrail] ASC) WITH (FILLFACTOR = 95)
);


GO

CREATE NONCLUSTERED INDEX [idxIDPool]
    ON [dbo].[AuditTrail]([IDPool] ASC);


GO

CREATE NONCLUSTERED INDEX [idIDSerializationTemplate]
    ON [dbo].[AuditTrail]([IDSerializationTemplate] ASC);


GO

