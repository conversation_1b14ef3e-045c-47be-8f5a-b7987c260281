CREATE PROCEDURE [dbo].[sp_CreateExternalRequest2] (
	@ReferenceID nvarchar(50), 
	@IDPool int,
	@IDOrder int, 
	@RequestedQnt int, 
	@LgnName nvarchar(128),
	@IDOrderRequest int output,
	@AllocCount int output)
AS
BEGIN
	SET NOCOUNT ON;

	INSERT INTO [OrderRequest] (ReferenceID, IDPool, RequestedQty, AllocatedQty, TimestampCreated, UserCreated )  
	VALUES (@ReferenceID, @IDPool, @RequestedQnt, 0, GETDATE(), @LgnName);  

	SELECT @IDOrderRequest = CAST(SCOPE_IDENTITY() AS INT)

	update top (@RequestedQnt) Serial 
	set IDOrderRequest = @IDOrderRequest,
		IsActive = 0
	where IDPool = @IDPool 
	  and IDRequest = 0
	  and IDOrderRequest is null;
	

	SELECT @AllocCount = @@ROWCOUNT;
	
	update OrderRequest 
	set AllocatedQty = @AllocCount, 
	  TimestampUpdated = GETDATE(), 
	  UserUpdated = @LgnName 
	where IDOrderRequest = @IDOrderRequest

	exec spSetPoolValues @IDPool

	SELECT S.SerialNumber FROM Serial S WHERE IDOrderRequest = @IDOrderRequest;
END

GO

