CREATE TABLE [dbo].[CustomsRegister] (
    [IDCustomsRegister] INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]        INT            NOT NULL,
    [SystemSubjectID]   NVARCHAR (50)  NOT NULL,
    [FavoriteName]      NVARCHAR (250) NULL,
    [INN]               NVARCHAR (250) NULL,
    [OrgName]           NVARCHAR (250) NULL,
    [WarehouseAddress]  NVARCHAR (250) NULL,
    [CustomCode]        NVARCHAR (250) NULL,
    [CustomName]        NVARCHAR (250) NULL,
    [WarehouseType]     NVARCHAR (250) NULL,
    [TimestampCreated]  DATETIME       CONSTRAINT [DF_CustomsRegister_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [TimestampUpdated]  DATETIME       NULL,
    [UserCreated]       NVARCHAR (128) NOT NULL,
    [UserUpdated]       NVARCHAR (128) NULL,
    CONSTRAINT [PK_CustomsRegister] PRIMARY KEY CLUSTERED ([IDCustomsRegister] ASC),
    CONSTRAINT [FK_CustomsRegister_Customer] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE
);


GO

