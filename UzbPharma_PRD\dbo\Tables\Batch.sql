CREATE TABLE [dbo].[Batch] (
    [IDBatch]          INT            IDENTITY (1, 1) NOT NULL,
    [IDProduct]        INT            NOT NULL,
    [ControlID]        NVARCHAR (200) NULL,
    [ExpiryDate]       DATETIME       NOT NULL,
    [PackingID]        NVARCHAR (200) NULL,
    [BatchID]          NVARCHAR (100) NOT NULL,
    [UsageType]        NVARCHAR (100) NULL,
    [TimestampCreated] DATETIME       CONSTRAINT [DF_Batch_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]      NVARCHAR (128) NOT NULL,
    [TimestampUpdated] DATETIME       NULL,
    [UserUpdated]      NVARCHAR (128) NULL,
    [OwnerID]          NVARCHAR (200) NULL,
    [OrderType]        INT            NULL,
    [ProductionDate]   DATETIME       NULL,
    CONSTRAINT [PK_Batch] PRIMARY KEY CLUSTERED ([IDBatch] ASC),
    CONSTRAINT [FK_Batch_Product] FOREIGN KEY ([IDProduct]) REFERENCES [dbo].[Product] ([IDProduct]) ON UPDATE CASCADE
);


GO

