

CREATE PROCEDURE [dbo].[rpt_GetReportedSerialsByTimestamp]
(
    @FromTimestamp Datetime, @ToTimestamp Datetime
)
AS
BEGIN
	select
		  p.IDCustomer,    
		  count(isnull(s.IDBatchEventCode,0)) as [Count]   
	from BatchEvent be with(nolock) 
	join Batch b with (nolock) on b.IDBatch = be.IDBatch
	join Product p with (nolock) on p.IDProduct = b.IDProduct
	left join BatchEventCode as s with (nolock) on s.IDBatchEvent = be.IDBatchEvent
	where IDBatchEventType = 1 and be.TimestampCreated between @FromTimestamp and @ToTimestamp
	group by p.IDCustomer   

 
 END

GO

