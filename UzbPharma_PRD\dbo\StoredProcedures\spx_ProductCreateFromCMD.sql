
CREATE procedure [dbo].[spx_ProductCreateFromCMD](@IDProduct INT)
as
begin

	if exists (select top 1 * from MDProduct where IDProduct = @IDProduct)
	begin

		set identity_insert dbo.Product on

		 INSERT INTO dbo.Product
		         ([IDProduct]
		         ,[IDCustomer]
		         ,[CodeType]
		         ,[Code]
		         ,[Name]
		         ,[CommonName]
		         ,[Form]
		         ,[Strength]
		         ,[PackSize]
		         ,[PackType]
		         ,[RegDate]
		         ,[RegHolder]
		         ,[RegNumber]
		         ,[DrugCode]
		         ,[TimestampCreated]
		         ,[UserCreated]
		         ,[TimestampUpdated]
		         ,[UserUpdated]
		         ,[SerialNumberType]
		         ,[IsActive])
		 SELECT 
		         p.[IDProduct]
		        ,p.IDCustomer
		        ,p.[CodeType]
		        ,p.[Code]
		        ,p.[Name]
		        ,p.[CommonName]
		        ,p.[Form]
		        ,p.[Strength]
		        ,p.[PackSize]
		        ,p.[PackType]
		        ,NULL
		        ,NULL
		        ,NULL
		        ,NULL
		        ,p.[TimestampCreated]
		        ,p.[UserCreated]
		        ,p.[TimestampUpdated]
		        ,p.[UserUpdated]
		        ,p.[SerialNumberSourceType]
		        ,p.[IsActive]
		 FROM 
			MDProduct p with (nolock)
		 WHERE 
			p.IDProduct = @IDProduct

		 set identity_insert dbo.Product off

	end

end

GO

