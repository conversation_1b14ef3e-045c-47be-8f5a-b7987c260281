create procedure [dbo].[rptCustomerCodeCreate2024-Q2]  
as  
begin  
select c.Name, count(*) as cnt from BatchEventCode bec with (nolock)  
left join BatchEvent be with (nolock) on bec.IDBatchEvent=be.IDBatchEvent  
left join Batch b with (nolock) on b.IDBatch = be.IDBatch  
left join Product p with (nolock) on p.IDProduct=b.IDProduct  
left join Customer c with (nolock) on c.IDCustomer=p.IDCustomer  
where be.TimestampCreated between '2024-04-01' and '2024-06-30 23:59:59'  
group by c.Name  
order by 2 desc ,1 desc  
end

GO

