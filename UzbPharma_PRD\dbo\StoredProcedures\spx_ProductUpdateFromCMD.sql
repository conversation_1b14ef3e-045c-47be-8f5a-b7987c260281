CREATE procedure [dbo].[spx_ProductUpdateFromCMD](@IDProduct int)
as
begin

	if exists (select top 1 * from MDProduct where IDProduct = @IDProduct)
	begin

		UPDATE uz
		SET 
			uz.CommonName = cmdProduct.CommonName,
			uz.Form = cmdProduct.Form,
			uz.PackSize = cmdProduct.PackSize,
			uz.PackType = cmdProduct.PackType,
			uz.Strength = cmdProduct.Strength,
			uz.TimeStampUpdated = cmdProduct.TimestampUpdated,
			uz.UserUpdated = cmdProduct.UserUpdated,
			uz.IsActive = cmdProduct.IsActive,
			uz.SerialNumberType = cmdProduct.SerialNumberSourceType
		FROM MDProduct cmdProduct 
		INNER JOIN dbo.Product uz on cmdProduct.IDProduct = uz.IDProduct
		WHERE uz.IDProduct = @IDProduct

	end
end

GO

