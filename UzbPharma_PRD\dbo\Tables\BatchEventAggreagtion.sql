CREATE TABLE [dbo].[BatchEventAggreagtion] (
    [IDBatchEventAggreagtion] INT            IDENTITY (1, 1) NOT NULL,
    [IDBatchEvent]            INT            NOT NULL,
    [LevelType]               NVARCHAR (50)  NULL,
    [LevelName]               NVARCHAR (50)  NULL,
    [LevelCount]              INT            NULL,
    [UnitCapacity]            INT            NULL,
    [UnitName]                NVARCHAR (50)  NULL,
    [SerialType]              NVARCHAR (50)  NULL,
    [SerialItemType]          NVARCHAR (50)  NULL,
    [SerialLevel]             NVARCHAR (50)  NULL,
    [SerialCode]              NVARCHAR (50)  NULL,
    [SerialItemCount]         INT            NULL,
    [TimestampProduced]       DATETIME       NULL,
    [SerialLineName]          NVARCHAR (50)  NULL,
    [TimestampCreated]        DATETIME       NOT NULL,
    [UserCreated]             NVARCHAR (128) NOT NULL,
    [TimestampUpdated]        DATETIME       NULL,
    [UserUpdated]             NVARCHAR (128) NULL,
    CONSTRAINT [PK_BatchEventAggreagtion] PRIMARY KEY CLUSTERED ([IDBatchEventAggreagtion] ASC),
    CONSTRAINT [FK_BatchEventAggreagtion_BatchEvent] FOREIGN KEY ([IDBatchEvent]) REFERENCES [dbo].[BatchEvent] ([IDBatchEvent])
);


GO

