


CREATE PROCEDURE [dbo].[spx_CreateUserLogin_CMD](
	@LgnName NVARCHAR(128),
	@IDCustomer INT,
	@UserCreated NVARCHAR(128))
AS BEGIN
	declare @IDService int = (select IDService from MDService WHERE SrcSystem = 'UZ')

	if NOT EXISTS (select top 1 * from MDUserLogin 
		where LgnName = @LgnName)
	begin
		INSERT INTO MDUserLogin   ([LgnName], IDCustomer ,TimestampCreated, UserCreated)
		values
		(@LgnName, @IDCustomer, GETDATE(), @UserCreated)

		INSERT INTO MDServiceUserLogin
			   (LgnName
			   ,IDService)
		values
				(@LgnName,
				 @IDService)
	end
	else
	begin
		set @LgnName = (select top (1) LgnName from MDUserLogin WHERE LgnName = @LgnName)
	end
END

GO

