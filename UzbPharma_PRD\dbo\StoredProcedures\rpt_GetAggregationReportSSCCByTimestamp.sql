

create PROCEDURE [dbo].[rpt_GetAggregationReportSSCCByTimestamp]
( 
    @FromTimestamp Datetime, @ToTimestamp Datetime
)
AS
BEGIN
	select 
		p.IDCustomer,
		count(isnull(ba.IDBatchEventAggreagtion,0)) as [Count]
	from BatchEvent be with (nolock)
	join batch b with (nolock) on b.IDBatch = be.IDBatch
	join Product p with (nolock) on p.IDProduct = b.IDProduct
	left join BatchEventAggreagtion ba with (nolock) on be.IDBatchEvent = ba.IDBatchEvent
	where IDBatchEventType = 2  and be.TimestampCreated between @FromTimestamp and @ToTimestamp
	group by p.IDCustomer

 
 END

GO

