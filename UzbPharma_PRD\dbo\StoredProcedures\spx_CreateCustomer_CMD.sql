CREATE PROCEDURE [dbo].[spx_CreateCustomer_CMD](  
 @Name NVARCHAR(128),  
 @UserCreated NVARCHAR(128),  
 @IDCustomer int out)  
AS BEGIN  
   
  
  if NOT EXISTS (select top 1 * from MDCustomer  
   where IDCustomer = @IDCustomer)  
  begin  
   INSERT INTO MDCustomer  
       ([Name]  
       ,IDBusinessSegment  
       ,TimestampCreated  
       ,UserCreated)  
   values  
   (@Name, 1, GETDATE(), @UserCreated)   
  
   SET @IDCustomer = @@IDENTITY  
  
   INSERT INTO MDCustomerService  
    ([IDCustomer]  
    ,[IDService])  
     VALUES  
    (@IDCustomer  
    ,(SELECT TOP(1) IDService FROM MDService where SrcSystem = 'UZ'))  
  
     -- Create customer in local table dbo.customer  
  set identity_insert dbo.customer on  
  insert into dbo.Customer  
      (IDCustomer  
   ,[Name]  
   ,TimeStampCreated  
   ,UserCreated)  
  values  
   (@IDCustomer  
   ,@Name  
   ,GETDATE()  
   ,@UserCreated)  
  set identity_insert dbo.Customer off  
  
  end  
  else  
  begin  
   set @IDCustomer = (select top 1 IDCustomer from MDCustomer WHERE IDCustomer = @IDCustomer)  
  
   -- Create customer in local table base.customer  
   set identity_insert dbo.customer on  
   insert into dbo.Customer  
       (IDCustomer  
    ,[Name]  
    ,TimeStampCreated  
    ,UserCreated)  
   values  
    (@IDCustomer  
    ,@Name  
    ,GETDATE()  
    ,@UserCreated)  
   set identity_insert dbo.Customer off  
  end  
   
  
END

GO

