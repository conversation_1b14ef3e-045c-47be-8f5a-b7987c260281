CREATE TABLE [dbo].[BatchEventStatus] (
    [IDBatchEventStatus] INT            IDENTITY (1, 1) NOT NULL,
    [IDBatchEvent]       INT            NOT NULL,
    [FieldName]          NVARCHAR (256) NULL,
    [FieldValue]         NVARCHAR (256) NULL,
    [IsError]            BIT            CONSTRAINT [DF_BatchEventStatus_IsError] DEFAULT ((0)) NOT NULL,
    [IsInfo]             BIT            CONSTRAINT [DF_BatchEventStatus_IsInfo] DEFAULT ((0)) NOT NULL,
    [IsWarrning]         BIT            CONSTRAINT [DF_BatchEventStatus_IsWarning] DEFAULT ((0)) NOT NULL,
    [TimestampCreated]   DATETIME       NOT NULL,
    [UserCreated]        NVARCHAR (128) NOT NULL,
    CONSTRAINT [PK_BatchEventStatus] PRIMARY KEY CLUSTERED ([IDBatchEventStatus] ASC),
    CONSTRAINT [FK_BatchEventStatus_BatchEvent] FOREIGN KEY ([IDBatchEvent]) REFERENCES [dbo].[BatchEvent] ([IDBatchEvent])
);


GO

