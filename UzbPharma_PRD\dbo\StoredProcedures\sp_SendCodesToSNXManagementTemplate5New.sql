CREATE PROCEDURE [dbo].[sp_SendCodesToSNXManagementTemplate5New] (@IDOrder int, @IDOrderProduct int, @LgnName nvarchar(128))
AS
BEGIN
	SET NOCOUNT ON;
	declare @TimestampUpdated datetime = GETDATE();

	declare @ParamDef nvarchar(max) = N'@pIDOrder int, @pLgnName nvarchar(128), @pTimestampUpdated datetime, @pIDOrderProduct int';

	declare @SQL nvarchar(max) = 
	N'
	declare @IDProduct int = (select top 1 IDProduct from OrderProduct where IDOrderProduct=@pIDOrderProduct)

	declare @TmpCodes table 
		(
			GTIN nvarchar(14),
			SerialNumber nvarchar(13) collate Cyrillic_General_CS_AS not null primary key,
			AI91 nvarchar(100),
			AI92 nvarchar(100)
		)

	declare @TmpSerial table 
		(
			IDSerial bigint not null primary key,
			AI91 nvarchar(100),
			AI92 nvarchar(100)
		)

	BEGIN TRANSACTION Tran1
		
		BEGIN TRY

			insert into @TmpCodes (GTIN, SerialNumber, AI91, AI92)
			select SUBSTRING ( cp.Code , 3 , 14 ) as GTIN, 
					SerialNumber   = SUBSTRING ( cp.Code , 19 , 13 ) collate Cyrillic_General_CS_AS ,
					SUBSTRING ( cp.Code , 35 , 4 ) as AI91,
					SUBSTRING ( cp.Code , 42 , 44 ) as AI92
			from OrderProductCryptoCode cp with (nolock)
			where IDOrderProduct = @pIDOrderProduct;

			insert into @TmpSerial(IDSerial, AI91, AI92)
			select s.IDSerial, c.AI91, c.AI92
			from SGCloudSNGenerator_PRD.dbo.Serial s with (nolock)
			inner join @TmpCodes c on c.SerialNumber = s.SerialNumber 
			where s.IDProduct=@IDProduct 
			and s.IDOrderRequest in (select IDSNXOrderRequest from OrderProductPoolRequest with (nolock) where IDOrderProduct = @pIDOrderProduct )

			update s
			set 
				s.AI91 = c.AI91,
				s.AI92 = c.AI92,
				s.UserUpdated = @pLgnName,
				s.TimestampUpdated = @pTimestampUpdated,
				s.IsActive = 1
			from SGCloudSNGenerator_PRD.dbo.Serial s
			inner join @TmpSerial c on s.IDSerial = c.IDSerial;
	
			COMMIT TRANSACTION [Tran1]

		END TRY
		BEGIN CATCH
			
			ROLLBACK TRANSACTION [Tran1]
		
		END CATCH

	declare @IDPool int;

	DECLARE cursor_ForPools CURSOR FOR
	select distinct IDPool 
	from SGCloudSNGenerator_PRD.dbo.Serial s
	join OrderProductPoolRequest r on r.IDSNXOrderRequest = s.IDOrderRequest and r.IDOrderProduct = @pIDOrderProduct

	OPEN cursor_ForPools;

	FETCH NEXT FROM cursor_ForPools INTO @IDPool;

	WHILE @@FETCH_STATUS = 0  
    BEGIN
		BEGIN TRANSACTION [Tran2]

			BEGIN TRY
		
				exec SGCloudSNGenerator_PRD.dbo.spSetPoolValues @IDPool
				FETCH NEXT FROM cursor_ForPools INTO @IDPool;  
			
				COMMIT TRANSACTION [Tran2]

			END TRY

			BEGIN CATCH

				ROLLBACK TRANSACTION [Tran2]

			END CATCH  

    END;

	CLOSE cursor_ForPools;

	DEALLOCATE cursor_ForPools;

	';

	exec sp_executesql @SQL, @ParamDef, @pIDOrder = @IDOrder, @pLgnName = @LgnName, @pTimestampUpdated = @TimestampUpdated, @pIDOrderProduct = @IDOrderProduct;


END

GO

