CREATE PROCEDURE [dbo].[sp_SendCodesToSNXManagementTemplate5CreatePool] (@IDOrder int, @ExpiryDate datetime, @OrderID nvarchar(50), @IDCustomer int, @IDProduct int, @IDOrderProduct int, @LgnName nvarchar(128))  
AS  
BEGIN  
  
  
  declare @ParamDef nvarchar(max) = N'@pIDOrder int, @pExpiryDate datetime, @pOrderID nvarchar(50), @pLgnName nvarchar(128), @pIDCustomer int, @pIDProduct int, @pIDOrderProduct int';  
  
  declare @SQL nvarchar(max) =   
  N'  
  declare @SerialsCnt int,  
    @IDPool int  
  
   declare @TmpCodes table  
  (  
   GTIN nvarchar(14),  
   SerialNumber nvarchar(13) collate Cyrillic_General_CS_AS not null primary key ,  
   AI91 nvarchar(100),  
   AI92 nvarchar(100)  
  )   
  
  BEGIN TRANSACTION Tran1  
    
  BEGIN TRY  
  
   if (exists(select top 1 IDPool from SGCloudSNGenerator_PRD.dbo.[Pool] where Code = @pOrderID))  
    RAISERROR(''Error! Pool with this code already exists.'', 17, 1);  
     
   select @SerialsCnt = COUNT(IDOrderCryptoCode) from OrderProductCryptoCode where IDOrderProduct = @pIDOrderProduct  
   if (@SerialsCnt < 1)  
    RAISERROR(''Error! No serial numbers found.'', 17, 1);  
  
   INSERT INTO SGCloudSNGenerator_PRD.dbo.[Pool] (IDCustomer, IDProduct, Name, Code, Prefix, IsRandom, Sufix, CharSet, [Length],   
                [Count], Used, Free, Threshold, IsActive, TimestampCreated, UserCreated,  
                FreeGen, IsImported, PoolExpiryDate, SerializationType )  
   VALUES (@pIDCustomer, @pIDProduct, ''OMS Operator '' + CONVERT(varchar(10), @pIDOrder), @pOrderID, '''', 0, '''', ''0'', 13,   
                @SerialsCnt, 0, @SerialsCnt, 100, 0, GETDATE(), @pLgnName,  
                0, 1, @pExpiryDate, ''RUSSIAN'')  
  
   SELECT @IDPool = CAST(SCOPE_IDENTITY() AS INT)  
  
   insert into @TmpCodes (GTIN, SerialNumber, AI91, AI92)  
   select SUBSTRING ( cp.Code , 3 , 14 ) as GTIN,   
       SUBSTRING ( cp.Code , 19 , 13 ) as SerialNumber,  
       SUBSTRING ( cp.Code , 35 , 4 ) as AI91,  
       SUBSTRING ( cp.Code , 42 , 44 ) as AI92  
   from OrderProductCryptoCode cp  
   where IDOrderProduct = @pIDOrderProduct;  
  
   INSERT INTO SGCloudSNGenerator_PRD.dbo.Serial (IDRequest, IDProduct, IDPool, SerialNumber, IsActive, TimestampCreated, UserCreated, AI91, AI92)   
   select 0, @pIDProduct, @IDPool, c.SerialNumber, 1, GETDATE(), @pLgnName, c.AI91, c.AI92  
   from @TmpCodes c   
  
   exec SGCloudSNGenerator_PRD.dbo.[spSetPoolValues] @IDPool   
   
   Update SGCloudSNGenerator_PRD.dbo.[Pool]  
   set IsActive = 1  
   where IDPool = @IDPool  
  
   COMMIT TRANSACTION [Tran1]  
  
  END TRY  
  BEGIN CATCH  
   DECLARE   
    @ErrorMessage  NVARCHAR(4000),   
    @ErrorSeverity INT,   
    @ErrorState    INT;  
  
   SELECT   
    @ErrorMessage = ERROR_MESSAGE(),   
    @ErrorSeverity = ERROR_SEVERITY(),   
    @ErrorState = ERROR_STATE();  
  
   ROLLBACK TRANSACTION [Tran1]  
  
   RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);  
    
  END CATCH  
  
  ';  
   
 exec sp_executesql @SQL, @ParamDef, @pIDOrder = @IDOrder, @pExpiryDate = @ExpiryDate, @pOrderID = @OrderID, @pLgnName = @LgnName, @pIDCustomer = @IDCustomer, @pIDProduct = @IDProduct, @pIDOrderProduct = @IDOrderProduct;  
  
    
    
     
    
  
  
END

GO

