CREATE TABLE [dbo].[CountryRegister] (
    [IDCountryRegister] INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]        INT            NULL,
    [ISOCode]           NVARCHAR (3)   NULL,
    [A2Code]            NVARCHAR (2)   NULL,
    [Location]          NVARCHAR (128) NULL,
    [A3Code]            NVARCHAR (3)   NULL,
    [FullName]          NVARCHAR (500) NULL,
    [LocationPrecise]   NVARCHAR (255) NULL,
    [EngName]           NVARCHAR (255) NULL,
    [RuName]            NVARCHAR (255) NULL,
    [ID]                NVARCHAR (50)  NULL,
    [TimestampCreated]  DATETIME       CONSTRAINT [DF_LocationRegister_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]       NVARCHAR (128) NULL,
    [UserUpdated]       NVARCHAR (128) NULL,
    [TimestampUpdated]  DATETIME       NULL,
    CONSTRAINT [PK_CountryRegister] PRIMARY KEY CLUSTERED ([IDCountryRegister] ASC),
    CONSTRAINT [FK_CountryRegister_Customer] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE
);


GO

