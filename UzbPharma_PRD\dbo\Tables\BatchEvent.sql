CREATE TABLE [dbo].[BatchEvent] (
    [ID<PERSON>atchEvent]      INT            IDENTITY (1, 1) NOT NULL,
    [IDBatch]           INT            NOT NULL,
    [IDBatchEventType]  SMALLINT       NOT NULL,
    [DistributionState] NVARCHAR (50)  NULL,
    [ReportID]          NVARCHAR (100) NULL,
    [Error]             NVARCHAR (500) NULL,
    [TimestampCreated]  DATETIME       CONSTRAINT [DF_BatchEvent_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]       NVARCHAR (128) NOT NULL,
    [TimestampUpdated]  DATETIME       NULL,
    [UserUpdated]       NVARCHAR (128) NULL,
    [IDSgXmlFile]       INT            NULL,
    CONSTRAINT [PK_BatchEvent] PRIMARY KEY CLUSTERED ([IDBatchEvent] ASC),
    CONSTRAINT [FK_BatchEvent_Batch] FOREIGN KEY ([IDBatch]) REFERENCES [dbo].[Batch] ([IDBatch]),
    CONSTRAINT [FK_BatchEvent_BatchEventType] FOREIGN KEY ([IDBatchEventType]) REFERENCES [dbo].[BatchEventType] ([IDBatchEventType])
);


GO

