CREATE PROCEDURE [dbo].[sp_SendCodesToSNXManagementTemplate5] (@IDOrder int, @IDOrderProduct int, @LgnName nvarchar(128))
AS
BEGIN
	SET NOCOUNT ON;
	declare @TimestampUpdated datetime = GETDATE();

	declare @ParamDef nvarchar(max) = N'@pIDOrder int, @pLgnName nvarchar(128), @pTimestampUpdated datetime, @pIDOrderProduct int';

	declare @SQL nvarchar(max) = 
	N'
	declare @TmpCodes table
	(
		GTIN nvarchar(14),
		SerialNumber nvarchar(13) not null primary key,
		AI91 nvarchar(100),
		AI92 nvarchar(100)
	)

	BEGIN TRANSACTION Tran1
		
		BEGIN TRY

			insert into @TmpCodes (GTIN, SerialNumber, AI91, AI92)
			select SUBSTRING ( cp.Code , 3 , 14 ) as GTIN, 
				   SUBSTRING ( cp.Code , 19 , 13 ) as <PERSON><PERSON><PERSON><PERSON><PERSON>,
				   SUBSTRING ( cp.Code , 35 , 4 ) as AI91,
				   SUBSTRING ( cp.Code , 42 , 44 ) as AI92
			from OrderProductCryptoCode cp
			where IDOrderProduct = @pIDOrderProduct;

			update s
			set 
				s.AI91 = c.AI91,
				s.AI92 = c.AI92,
				s.UserUpdated = @pLgnName,
				s.TimestampUpdated = @pTimestampUpdated,
				s.IsActive = 1
			from SGCloudSNGenerator_PRD.dbo.Serial s
			join OrderProductPoolRequest r on r.IDSNXOrderRequest = s.IDOrderRequest and r.IDOrderProduct = @pIDOrderProduct
			join @TmpCodes c on c.SerialNumber = s.SerialNumber collate SQL_Latin1_General_CP1_CS_AS
			;
	
			COMMIT TRANSACTION [Tran1]

		END TRY
		BEGIN CATCH
			
			ROLLBACK TRANSACTION [Tran1]
		
		END CATCH

	declare @IDPool int;

	DECLARE cursor_ForPools CURSOR FOR
	select distinct IDPool 
	from SGCloudSNGenerator_PRD.dbo.Serial s
	join OrderProductPoolRequest r on r.IDSNXOrderRequest = s.IDOrderRequest and r.IDOrderProduct = @pIDOrderProduct

	OPEN cursor_ForPools;

	FETCH NEXT FROM cursor_ForPools INTO @IDPool;

	WHILE @@FETCH_STATUS = 0  
    BEGIN
		BEGIN TRANSACTION [Tran2]

			BEGIN TRY
		
				exec SGCloudSNGenerator_PRD.dbo.spSetPoolValues @IDPool
				FETCH NEXT FROM cursor_ForPools INTO @IDPool;  
			
				COMMIT TRANSACTION [Tran2]

			END TRY

			BEGIN CATCH

				ROLLBACK TRANSACTION [Tran2]

			END CATCH  

    END;

	CLOSE cursor_ForPools;

	DEALLOCATE cursor_ForPools;

	';

	exec sp_executesql @SQL, @ParamDef, @pIDOrder = @IDOrder, @pLgnName = @LgnName, @pTimestampUpdated = @TimestampUpdated, @pIDOrderProduct = @IDOrderProduct;


END

GO

