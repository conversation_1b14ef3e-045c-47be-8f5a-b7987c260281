CREATE TABLE [dbo].[Batch<PERSON>ventCode] (
    [IDBatch<PERSON>ventCode] BIGINT         IDENTITY (1, 1) NOT NULL,
    [IDBatchEvent]     INT            NOT NULL,
    [TimestampCreated] DATETIME       CONSTRAINT [DF_BatchEventCode_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]      NVARCHAR (128) NOT NULL,
    [CryptoCodeState]  NVARCHAR (50)  CONSTRAINT [DF_BatchEventCode_CryptoCodeState] DEFAULT ('OK') NOT NULL,
    [CryptoKey]        NVARCHAR (100) NOT NULL,
    [CryptoCode]       NVARCHAR (100) NOT NULL,
    [SerialNumber]     NVARCHAR (100) NOT NULL,
    CONSTRAINT [PK_BatchEventCode] PRIMARY KEY CLUSTERED ([IDBatchEventCode] ASC)
);


GO

CREATE NONCLUSTERED INDEX [idx_IDBatchEvent]
    ON [dbo].[BatchEventCode]([IDBatchEvent] ASC);


GO

