create procedure [dbo].[spx_CustomerCreateFromCMD](@IDCustomer int)
as
begin
	if exists (select top 1 * from MDCustomer with (nolock) where IDCustomer = @IDCustomer)
	begin

		set identity_insert dbo.Customer on 

			INSERT INTO dbo.Customer
				      ([IDCustomer]
				      ,[IDCustomerType]
				      ,[Name]
				      ,[OmsID]
				      ,[Username]
				      ,[Password]
				      ,[OmsUrl]
				      ,[OmsToken]
				      ,[MdlpUserID]
				      ,[MdlpPassword]
				      ,[ClientID]
				      ,[ClientSecret]
				      ,[MdlpUrl]
				      ,[MdlpToken]
				      ,[MdlpTokenExpiryDate]
				      ,[MdlpMahID]
				      ,[CertificateThumbprint]
				      ,[TimestampCreated]
				      ,[UserCreated]
				      ,[TimestampUpdated]
				      ,[UserUpdated]
				      ,[MdlpPlaceID])
				   SELECT 
				      @IDCustomer
				      ,2
				      ,c.Name
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,NULL
				      ,c.TimestampCreated
				      ,c.UserCreated
				      ,c.TimestampUpdated
				      ,c.UserUpdated
				      ,NULL
				FROM 
					MDCustomer c 
				WHERE 
					c.IDCustomer = @IDCustomer

		set identity_insert dbo.Customer off

	end
	
end

GO

