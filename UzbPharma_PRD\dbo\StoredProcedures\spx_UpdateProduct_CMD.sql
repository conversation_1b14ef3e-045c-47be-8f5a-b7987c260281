
CREATE PROCEDURE [dbo].[spx_UpdateProduct_CMD](
	@IDCustomer INT, 
	@CodeType nvarchar(10), 
	@Code nvarchar(14), 
	@Name nvarchar(255), 
	@CommonName nvarchar(255) = null,
	@Form nvarchar(100) = null, 
	@PackType nvarchar(50) = null, 
	@PackSize int, 
	@Strength nvarchar(100) = null, 
	@UserUpdated nvarchar(128), 
	@IsActive bit, 
	@SerialNumberType nvarchar(50),
	@IDProduct int)
AS BEGIN
		
	DECLARE @IDServiceMasterRecordUZ INT = (SELECT TOP(1) IDService FROM MDService
										    WHERE SrcSystem = 'UZ');
	
	UPDATE MDProduct
		SET Name = @Name,
			CommonName = @CommonName,
			Form = @Form,
			Strength = @Strength,
			PackSize = @PackSize,
			PackType = @PackType,
			IsActive = @IsActive,
			SerialNumberSourceType = @SerialNumberType,
			UserUpdated = @UserUpdated,
			TimestampUpdated = GETDATE()
		where IDCustomer = @IDCustomer and IDServiceMasterRecord = @IDServiceMasterRecordUZ
			and CodeType = @CodeType and Code = @Code and IDProduct = @IDProduct

END

GO

