CREATE TABLE [dbo].[CustomerService] (
    [IDCustomerService]     INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]            INT            NOT NULL,
    [Name]                  NVARCHAR (100) NOT NULL,
    [StartDate]             DATE           NOT NULL,
    [EndDate]               DATE           NULL,
    [IsTrial]               BIT            CONSTRAINT [DF_CustomerService_IsTrial] DEFAULT ((0)) NOT NULL,
    [TrialEndDate]          DATETIME       NULL,
    [IsContract]            BIT            CONSTRAINT [DF_CustomerService_IsContract] DEFAULT ((0)) NOT NULL,
    [ContractTerm]          DATE           NULL,
    [ContractData]          NVARCHAR (100) NULL,
    [ContractNotifyEndDays] INT            CONSTRAINT [DF_CustomerService_ContractNotifyEndDays] DEFAULT ((0)) NOT NULL,
    [IsPayed]               BIT            CONSTRAINT [DF_CustomerService_IsPayed] DEFAULT ((0)) NOT NULL,
    [PaymentTerm]           DATE           NULL,
    [PaymentNotifyDays]     INT            CONSTRAINT [DF_CustomerService_PaymentNotifyDays] DEFAULT ((0)) NOT NULL,
    [InvoiceData]           NVARCHAR (100) NULL,
    [IsCanceled]            BIT            CONSTRAINT [DF_CustomerService_IsCanceled] DEFAULT ((0)) NOT NULL,
    [CancelReason]          NVARCHAR (200) NULL,
    [TimestampCreated]      DATETIME       CONSTRAINT [DF_CustomerService_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]           NVARCHAR (128) CONSTRAINT [DF_CustomerService_UserCreated] DEFAULT (user_name()) NOT NULL,
    [TimestampUpdated]      DATETIME       NULL,
    [UserUpdated]           NVARCHAR (128) NULL,
    CONSTRAINT [PK_CustomerService] PRIMARY KEY CLUSTERED ([IDCustomerService] ASC),
    CONSTRAINT [FK_CustomerService_Customer] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [idx_Customer_Name_Date]
    ON [dbo].[CustomerService]([IDCustomer] ASC, [Name] ASC, [StartDate] ASC, [EndDate] ASC);


GO

