CREATE TABLE [dbo].[CustomerDocType] (
    [IDCustomerDocType] INT IDENTITY (1, 1) NOT NULL,
    [IDCustomer]        INT NOT NULL,
    [IDDocType]         INT NOT NULL,
    [IsNewAllowed]      BIT CONSTRAINT [DF_CustomerDocType_IsNewAllowed] DEFAULT ((0)) NOT NULL,
    [IsConvertAllowed]  BIT CONSTRAINT [DF_CustomerDocType_IsConvertAllowed] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_CustomerDocType] PRIMARY KEY CLUSTERED ([IDCustomerDocType] ASC),
    CONSTRAINT [FK_CustomerDocType_Customer] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE,
    CONSTRAINT [FK_CustomerDocType_DocType] FOREIGN KEY ([IDDocType]) REFERENCES [dbo].[DocType] ([IDDocType])
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [idxIDCustomerIDDocTypeUnique]
    ON [dbo].[CustomerDocType]([IDCustomer] ASC, [IDDocType] ASC);


GO

