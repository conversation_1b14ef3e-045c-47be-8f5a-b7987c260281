CREATE TABLE [dbo].[AuditTrail] (
    [IDAuditTrail]  INT            IDENTITY (1, 1) NOT NULL,
    [Action]        NVARCHAR (100) NULL,
    [IDTable]       NVARCHAR (50)  NULL,
    [TableName]     NVARCHAR (100) NULL,
    [Timestamp]     DATETIME       NULL,
    [User]          NVARCHAR (128) NULL,
    [DisplayName]   NVARCHAR (100) NULL,
    [IDTableDetail] NVARCHAR (50)  NULL,
    CONSTRAINT [PK_AuditTrail] PRIMARY KEY CLUSTERED ([IDAuditTrail] ASC)
);


GO

CREATE NONCLUSTERED INDEX [idxUser]
    ON [dbo].[AuditTrail]([User] ASC, [Timestamp] ASC);


GO

CREATE NONCLUSTERED INDEX [idxIDTableName]
    ON [dbo].[AuditTrail]([TableName] ASC, [IDTable] ASC);


GO

