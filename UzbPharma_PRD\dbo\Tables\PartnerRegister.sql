CREATE TABLE [dbo].[PartnerRegister] (
    [IDPartnerRegister] INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]        INT            NOT NULL,
    [SystemSubjectID]   NVARCHAR (50)  NOT NULL,
    [FavoriteName]      NVARCHAR (250) NULL,
    [Name]              NVARCHAR (250) NULL,
    [Country]           NVARCHAR (50)  NULL,
    [Address]           NVARCHAR (50)  NULL,
    [RegEntityType]     INT            CONSTRAINT [DF_PartnerRegister_RegEntityType] DEFAULT ((1)) NOT NULL,
    [TimestampCreated]  DATETIME       CONSTRAINT [DF_PartnerRegister_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [TimestampUpdated]  DATETIME       NULL,
    [UserCreated]       NVARCHAR (128) NOT NULL,
    [UserUpdated]       NVARCHAR (128) NULL,
    CONSTRAINT [PK_PartnerRegister] PRIMARY KEY CLUSTERED ([IDPartnerRegister] ASC),
    CONSTRAINT [FK_PartnerRegister_Customer] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE
);


GO

