CREATE TABLE [dbo].[UserLogin] (
    [LgnName]               NVARCHAR (50)  NOT NULL,
    [MdlpUserID]            NVARCHAR (50)  NULL,
    [MdlpPassword]          NVARCHAR (100) NULL,
    [ClientID]              NVARCHAR (50)  NULL,
    [ClientSecret]          NVARCHAR (50)  NULL,
    [MdlpToken]             NVARCHAR (50)  NULL,
    [MdlpTokenExpiryDate]   DATETIME       NULL,
    [IDCustomer]            INT            NOT NULL,
    [MdlpUsername]          NVARCHAR (50)  NULL,
    [MdlpFirstName]         NVARCHAR (50)  NULL,
    [MdlpLastName]          NVARCHAR (50)  NULL,
    [MdlpSysID]             NVARCHAR (50)  NULL,
    [CertificateThumbprint] NVARCHAR (100) NULL,
    [TimestampCreated]      DATETIME       CONSTRAINT [DF_UserLogin_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]           NVARCHAR (128) CONSTRAINT [DF_UserLogin_UserCreated] DEFAULT (suser_sname()) NOT NULL,
    [TimestampUpdated]      DATETIME       NULL,
    [UserUpdated]           NVARCHAR (128) NULL,
    CONSTRAINT [PK_base.UserLogin] PRIMARY KEY CLUSTERED ([LgnName] ASC),
    CONSTRAINT [FK_UserLogin_Customer] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE
);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'MDLP token expiration time, normally 30 minutes from token request.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserLogin', @level2type = N'COLUMN', @level2name = N'MdlpTokenExpiryDate';


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'MDLP Token is issued for 30 minutes, and must not be renewd before expiration time', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'UserLogin', @level2type = N'COLUMN', @level2name = N'MdlpToken';


GO

