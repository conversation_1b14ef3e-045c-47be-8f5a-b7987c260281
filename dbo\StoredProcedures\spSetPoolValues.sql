CREATE PROCEDURE [dbo].[spSetPoolValues]
    @IDPool INT
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @Used INT, @Free INT, @FreeGen INT, @TotalGen INT;

    -- Consolidate all counting operations into a single query for efficiency.
    SELECT
        @Used     = SUM(CASE WHEN ISNULL(IsActive, 0) = 1 AND IDRequest <> 0 THEN 1 ELSE 0 END),
        @Free     = SUM(CASE WHEN ISNULL(IsActive, 0) = 1 AND IDRequest = 0 THEN 1 ELSE 0 END),
        @FreeGen  = SUM(CASE WHEN IDRequest = 0 AND IDOrderRequest IS NULL THEN 1 ELSE 0 END),
        @TotalGen = COUNT(1) -- COUNT(*) is generally faster for counting all rows.
    FROM
        dbo.Serial WITH (NOLOCK, INDEX(idx_IDPool_IDRequest))
    WHERE
        IDPool = @IDPool;

    -- Update the Pool table with the calculated values.
    UPDATE dbo.Pool
    SET
        Used     = ISNULL(@Used, 0),
        Free     = ISNULL(@Free, 0),
        TotalGen = ISNULL(@TotalGen, 0),
        -- The CASE logic for FreeGen is preserved from the original procedure.
        FreeGen  = CASE WHEN IsImported = 1 THEN 0 ELSE ISNULL(@FreeGen, 0) END
    WHERE
        IDPool = @IDPool;

END
GO
