CREATE TABLE [dbo].[RegionRegister] (
    [IDRegionRegister] INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]       INT            NOT NULL,
    [Title]            NVARCHAR (255) NULL,
    [Code]             NVARCHAR (50)  NULL,
    [Key]              NVARCHAR (50)  NULL,
    [TimestampCreated] DATETIME       NOT NULL,
    [UserCreated]      NVARCHAR (128) NOT NULL,
    [TimestampUpdated] DATETIME       NULL,
    [UserUpdated]      NVARCHAR (128) NULL,
    CONSTRAINT [PK_AreaRegister] PRIMARY KEY CLUSTERED ([IDRegionRegister] ASC),
    CONSTRAINT [FK_RegionRegister_Customer] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE
);


GO

