CREATE TABLE [dbo].[Request] (
    [IDRequest]        INT            IDENTITY (1, 1) NOT NULL,
    [ReferenceID]      NVARCHAR (50)  NOT NULL,
    [IDPool]           INT            NOT NULL,
    [RequestedQty]     INT            NOT NULL,
    [AllocatedQty]     INT            NOT NULL,
    [Ready]            BIT            NOT NULL,
    [TimestampCreated] DATETIME       NOT NULL,
    [UserCreated]      NVARCHAR (120) NOT NULL,
    [TimestampUpdated] DATETIME       NULL,
    [UserUpdated]      NVARCHAR (120) NULL,
    [Error]            NVARCHAR (MAX) NULL,
    CONSTRAINT [PK_Request] PRIMARY KEY CLUSTERED ([IDRequest] ASC),
    CONSTRAINT [FK_Request_Pool] FOREIGN KEY ([IDPool]) REFERENCES [dbo].[Pool] ([IDPool])
);


GO

CREATE NONCLUSTERED INDEX [idxReferenceID]
    ON [dbo].[Request]([ReferenceID] ASC) WITH (FILLFACTOR = 95);


GO

CREATE TRIGGER [dbo].[RequestAudit]
   ON  [dbo].[Request]
   AFTER INSERT,DELETE,UPDATE
AS 
BEGIN
	SET NOCOUNT ON;

	begin try
		-- process inserted records

	insert into dbo.AuditTrail([Action], [IDPool], [IDRequest], [TableName], [FieldName], [OldValue], [NewValue], [User])
	select * from (

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[IDRequest] = ins.IDRequest,
			[TableName] = 'dbo.Request',
			[FieldName] = 'ReferenceID',
			OldValue = cast(del.ReferenceID as nvarchar(max)),
			NewValue = cast(ins.ReferenceID as nvarchar(max)),
			[User] = ins.UserUpdated
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDRequest = ins.IDRequest
		where isnull(ins.ReferenceID,'') <> isnull(del.ReferenceID,'') or del.IDRequest is null

		union all 

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[IDRequest] = ins.IDRequest,
			[TableName] = 'dbo.Request',
			[FieldName] = 'RequestedQty',
			OldValue = cast(del.[RequestedQty] as nvarchar(max)),
			NewValue = cast(ins.[RequestedQty] as nvarchar(max)),
			[User] = ins.UserUpdated
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDRequest = ins.IDRequest
		where isnull(ins.[RequestedQty],'') <> isnull(del.[RequestedQty],'') or del.IDRequest is null
	
		union all 

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[IDRequest] = ins.IDRequest,
			[TableName] = 'dbo.Request',
			[FieldName] = 'AllocatedQty',
			OldValue = cast(del.[AllocatedQty] as nvarchar(max)),
			NewValue = cast(ins.[AllocatedQty] as nvarchar(max)),
			[User] = ins.UserUpdated
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDRequest = ins.IDRequest
		where isnull(ins.[AllocatedQty],'') <> isnull(del.[AllocatedQty],'') or del.IDRequest is null
	
		union all 

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[IDRequest] = ins.IDRequest,
			[TableName] = 'dbo.Request',
			[FieldName] = 'Ready',
			OldValue = cast(del.[Ready] as nvarchar(max)),
			NewValue = cast(ins.[Ready] as nvarchar(max)),
			[User] = ins.UserUpdated
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDRequest = ins.IDRequest
		where isnull(ins.[Ready],'') <> isnull(del.[Ready],'') or del.IDRequest is null
	
		union all 

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[IDRequest] = ins.IDRequest,
			[TableName] = 'dbo.Request',
			[FieldName] = 'Error',
			OldValue = cast(del.[Error] as nvarchar(max)),
			NewValue = cast(ins.[Error] as nvarchar(max)),
			[User] = ins.UserUpdated
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDRequest = ins.IDRequest
		where isnull(ins.[Error], '') <> isnull(del.[Error], '') or del.IDRequest is null

		) sel 
		
	where isnull(sel.NewValue, '') <> ISNULL(sel.OldValue, '')

		/* DEBUG
		select ins.*, del.* from inserted ins with (nolock)	
		left join deleted del with (nolock) on del.idevent = ins.idevent
		where ins.[NewBatchID] <> del.[NewBatchID] 

		select * from deleted
		select * from #tmp

		END DEBUG */


	end try

	begin catch
		-- CREATE ERROR LOG

	end catch
	
END

GO

