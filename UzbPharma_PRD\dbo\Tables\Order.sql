CREATE TABLE [dbo].[Order] (
    [IDOrder]                   INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]                INT            NOT NULL,
    [SubjectID]                 NVARCHAR (50)  NULL,
    [ExpectedCompleteTimestamp] DATETIME       NULL,
    [OrderID]                   NVARCHAR (50)  NULL,
    [TimestampCreated]          DATETIME       CONSTRAINT [DF_Order_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]               NVARCHAR (128) NOT NULL,
    [TimestampUpdated]          DATETIME       NULL,
    [UserUpdated]               NVARCHAR (128) NULL,
    [Description]               NVARCHAR (300) NULL,
    [FreeCode]                  BIT            CONSTRAINT [DF_Order_FreeCode] DEFAULT ((0)) NULL,
    [IDOrderPaymentType]        INT            NULL,
    [ProductionOrderId]         NVARCHAR (50)  NULL,
    [Contact<PERSON>erson]             NVARCHAR (128) NULL,
    CONSTRAINT [PK_Order] PRIMARY KEY CLUSTERED ([IDOrder] ASC),
    CONSTRAINT [FK_Order_Customer] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE
);


GO

