CREATE TABLE [dbo].[OrderProductCryptoCode] (
    [IDOrderCryptoCode] INT            IDENTITY (1, 1) NOT NULL,
    [IDOrderProduct]    INT            NOT NULL,
    [BlockID]           NVARCHAR (50)  NOT NULL,
    [Code]              NVARCHAR (256) NOT NULL,
    [TimestampCreated]  DATETIME       CONSTRAINT [DF_CodeResponse_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]       NVARCHAR (128) NOT NULL,
    CONSTRAINT [PK_CodeResponse] PRIMARY KEY CLUSTERED ([IDOrderCryptoCode] ASC),
    CONSTRAINT [FK_CodeResponse_OrderProduct] FOREIGN KEY ([IDOrderProduct]) REFERENCES [dbo].[OrderProduct] ([IDOrderProduct])
);


GO

CREATE NONCLUSTERED INDEX [idx_IDOrderProduct]
    ON [dbo].[OrderProductCryptoCode]([IDOrderProduct] ASC);


GO

