CREATE TABLE [dbo].[Document] (
    [IDDocument]       INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]       INT            NOT NULL,
    [IDBatch]          INT            NULL,
    [IDDocType]        INT            NOT NULL,
    [MD<PERSON><PERSON>equestID]    NVARCHAR (100) NULL,
    [MDLPDocumentID]   NVARCHAR (100) NULL,
    [XSDVersion]       NVARCHAR (10)  NULL,
    [MDLPStatus]       NVARCHAR (50)  NULL,
    [IsFinished]       BIT            NOT NULL,
    [IsError]          BIT            NOT NULL,
    [Data]             NVARCHAR (MAX) NULL,
    [TimestampCreated] DATETIME       NOT NULL,
    [UserCreated]      NVARCHAR (128) NOT NULL,
    [TimestampUpdated] DATETIME       NULL,
    [UserUpdated]      NVARCHAR (128) NULL,
    CONSTRAINT [PK_Document] PRIMARY KEY CLUSTERED ([IDDocument] ASC),
    CONSTRAINT [FK_Document_Customer] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE,
    CONSTRAINT [FK_Document_DocType] FOREIGN KEY ([IDDocType]) REFERENCES [dbo].[DocType] ([IDDocType])
);


GO

CREATE NONCLUSTERED INDEX [idx_IDCustomer_Date_desc]
    ON [dbo].[Document]([IDDocument] ASC, [TimestampCreated] DESC);


GO

