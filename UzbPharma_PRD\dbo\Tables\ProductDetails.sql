CREATE TABLE [dbo].[ProductDetails] (
    [IDProductDetail] INT            IDENTITY (1, 1) NOT NULL,
    [ID]              NVARCHAR (50)  NULL,
    [GTIN]            NVARCHAR (14)  NULL,
    [RegStatus]       NVARCHAR (50)  NULL,
    [RegNumber]       NVARCHAR (50)  NULL,
    [RegDate]         NVARCHAR (30)  NULL,
    [ProdDescLabel]   NVARCHAR (128) NULL,
    [TypeForm]        NVARCHAR (50)  NULL,
    [ProdPack1EdName] NVARCHAR (100) NULL,
    [PackerAddress]   NVARCHAR (255) NULL,
    [ProdName]        NVARCHAR (200) NULL,
    [ProdSellName]    NVARCHAR (255) NULL,
    [ProdContent]     NVARCHAR (255) NULL,
    [ProdDesc]        NVARCHAR (255) NULL,
    [ProdPack1]       NVARCHAR (255) NULL,
    [ProdPack1Ed]     NVARCHAR (255) NULL,
    [RegEndDate]      NVARCHAR (30)  NULL,
    [ProdDName]       NVARCHAR (100) NULL,
    [ProdPack1Name]   NVARCHAR (255) NULL,
    [ProdPack2Name]   NVARCHAR (255) NULL,
    [ProdPack12]      NVARCHAR (50)  NULL,
    [TnVed]           NVARCHAR (500) NULL,
    [Gnvlp]           BIT            NULL,
    [MaxGnvlp]        NVARCHAR (50)  NULL,
    [MaxGnvlpRegDate] NVARCHAR (30)  NULL,
    [RegHolder]       NVARCHAR (200) NULL,
    [RegCountry]      NVARCHAR (50)  NULL,
    [Pack23Name]      NVARCHAR (255) NULL,
    [CountryPack23]   NVARCHAR (50)  NULL,
    [Pack23Code]      NVARCHAR (50)  NULL,
    [Pack23Address]   NVARCHAR (255) NULL,
    [QAName]          NVARCHAR (255) NULL,
    [QACountry]       NVARCHAR (50)  NULL,
    [QACode]          NVARCHAR (50)  NULL,
    [QAAddressName]   NVARCHAR (255) NULL,
    [ProdStatus]      NVARCHAR (50)  NULL,
    [IsMinZdrav]      BIT            NULL,
    [IsGS1]           BIT            NULL,
    [CostLimit]       NVARCHAR (50)  NULL,
    [RegINN]          NVARCHAR (255) NULL,
    [Completeness]    NVARCHAR (255) NULL,
    [ProdFormName]    NVARCHAR (100) NULL,
    [GlfName]         NVARCHAR (255) NULL,
    [GlfCountry]      NVARCHAR (50)  NULL,
    [IDProduct]       INT            NULL,
    CONSTRAINT [PK_ProductDetails] PRIMARY KEY CLUSTERED ([IDProductDetail] ASC),
    CONSTRAINT [FK_ProductDetails_Product] FOREIGN KEY ([IDProduct]) REFERENCES [dbo].[Product] ([IDProduct]) ON UPDATE CASCADE
);


GO

