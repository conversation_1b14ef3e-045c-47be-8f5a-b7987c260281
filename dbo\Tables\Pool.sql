CREATE TABLE [dbo].[Pool] (
    [IDPool]            INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]        INT            NOT NULL,
    [ID<PERSON>anufacturer]    INT            NULL,
    [IDProduct]         INT            NOT NULL,
    [Name]              NVARCHAR (50)  NOT NULL,
    [Code]              NVARCHAR (50)  NOT NULL,
    [Prefix]            NVARCHAR (20)  NOT NULL,
    [IsRandom]          BIT            NOT NULL,
    [Sufix]             NVARCHAR (20)  NOT NULL,
    [CharSet]           NVARCHAR (100) NOT NULL,
    [Length]            INT            NOT NULL,
    [Count]             INT            NOT NULL,
    [Used]              INT            NOT NULL,
    [Free]              INT            NOT NULL,
    [Threshold]         INT            NOT NULL,
    [IsActive]          BIT            NOT NULL,
    [PoolExpiryDate]    DATETIME       NULL,
    [TimestampCreated]  DATETIME       NOT NULL,
    [UserCreated]       NVARCHAR (128) NOT NULL,
    [TimestampUpdated]  DATETIME       NULL,
    [UserUpdated]       NVARCHAR (128) NULL,
    [LastStatus]        NVARCHAR (300) NULL,
    [Start]             NVARCHAR (50)  NULL,
    [Step]              INT            NULL,
    [SerializationType] NVARCHAR (30)  NULL,
    [FreeGen]           INT            CONSTRAINT [DF_Pool_FreeGen] DEFAULT ((0)) NOT NULL,
    [IsImported]        BIT            DEFAULT ((0)) NOT NULL,
    [TotalGen]          INT            CONSTRAINT [DF_Pool_TotalGen] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_Pool] PRIMARY KEY CLUSTERED ([IDPool] ASC)
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [idx_Customer_Product_Code]
    ON [dbo].[Pool]([IDCustomer] ASC, [IDProduct] ASC, [Code] ASC);


GO

CREATE TRIGGER [dbo].[PoolAudit]
   ON  [dbo].[Pool] 
   AFTER INSERT,DELETE,UPDATE
AS 
BEGIN
	SET NOCOUNT ON;

	begin try
		-- process inserted records


	insert into dbo.AuditTrail([Action], [IDPool], [TableName], [FieldName], [OldValue], [NewValue], [User])
	select * from (
		
		
		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Name',
			OldValue = cast(del.[Name] as nvarchar(max)),
			NewValue = cast(ins.[Name] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[Name],'') <> isnull(del.[Name],'') or del.IDPool is null

		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Code',
			OldValue = cast(del.Code as nvarchar(max)),
			NewValue = cast(ins.Code as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.Code,'') <> isnull(del.Code,'') or del.IDPool is null


		union all 		

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Prefix',
			OldValue = cast(del.[Prefix] as nvarchar(max)),
			NewValue = cast(ins.[Prefix] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[Prefix],'') <> isnull(del.[Prefix],'') or del.IDPool is null
		
		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Sufix',
			OldValue = cast(del.[Sufix] as nvarchar(max)),
			NewValue = cast(ins.[Sufix] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[Sufix],'') <> isnull(del.[Sufix],'') or del.IDPool is null	
		
		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'CharSet',
			OldValue = cast(del.CharSet as nvarchar(max)),
			NewValue = cast(ins.CharSet as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.CharSet,'') <> isnull(del.CharSet,'') or del.IDPool is null
		
		union all 

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Length',
			OldValue = cast(del.[Length] as nvarchar(max)),
			NewValue = cast(ins.[Length] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[Length],'') <> isnull(del.[Length],'') or del.IDPool is null

		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Count',
			OldValue = cast(del.[Count] as nvarchar(max)),
			NewValue = cast(ins.[Count] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[Count],'') <> isnull(del.[Count],'') or del.IDPool is null

		union all 		
		
		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Used',
			OldValue = cast(del.[Used] as nvarchar(max)),
			NewValue = cast(ins.[Used] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[Used],'') <> isnull(del.[Used],'') or del.IDPool is null	
		
		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Free',
			OldValue = cast(del.[Free] as nvarchar(max)),
			NewValue = cast(ins.[Free] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[Free],'') <> isnull(del.[Free],'') or del.IDPool is null

		union all 
			
		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Min. Available SN',
			OldValue = cast(del.[Threshold] as nvarchar(max)),
			NewValue = cast(ins.[Threshold] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[Threshold],'') <> isnull(del.[Threshold],'') or del.IDPool is null	
		
		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'IsActive',
			OldValue = cast(case del.IsActive when 0 then 'NO' else 'YES' end as nvarchar(max)),
			NewValue = cast(case ins.IsActive when 0 then 'NO' else 'YES' end as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[IsActive],'') <> isnull(del.[IsActive],'') or del.IDPool is null

		
		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'PoolExpiryDate',
			OldValue = cast(del.[PoolExpiryDate] as nvarchar(max)),
			NewValue = cast(ins.[PoolExpiryDate] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[PoolExpiryDate],'') <> isnull(del.[PoolExpiryDate],'') or del.IDPool is null

		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'LastStatus',
			OldValue = del.LastStatus,
			NewValue = ins.LastStatus,
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[LastStatus],'') <> isnull(del.[LastStatus],'') or del.IDPool is null	
		
		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Start',
			OldValue = cast(del.[Start] as nvarchar(max)),
			NewValue = cast(ins.[Start] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[Start],'') <> isnull(del.[Start],'') or del.IDPool is null	
		
		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Step',
			OldValue = cast(del.[Step] as nvarchar(max)),
			NewValue = cast(ins.[Step] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where isnull(ins.[Step],'') <> isnull(del.[Step],'') or del.IDPool is null	
		
		union all

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'SerializationType',
			OldValue = cast(stdel.SerializationType as nvarchar(max)),
			NewValue = cast(stins.[SerializationType] as nvarchar(max)),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		left outer join dbo.SerializationType stins on stins.IDSerializationType = (ins.SerializationType collate database_default)
		left outer join dbo.SerializationType stdel on stdel.IDSerializationType = (del.SerializationType collate database_default)
		where isnull(ins.[SerializationType],'') <> isnull(del.[SerializationType],'') or del.IDPool is null	
		
		union all 

		select 
			[Action] = case when del.IDPool is null then 'Create' else 'Update' end,
			[IDPool] = ins.IDPool,
			[TableName] = 'dbo.Pool',
			[FieldName] = 'Manufacturer',
			OldValue = CASE WHEN  del.IDPool is null THEN '' ELSE COALESCE(DelM.MahName, 'All manufacturers') END COLLATE DATABASE_DEFAULT,
			NewValue = COALESCE(InsM.MahName, 'All manufacturers') COLLATE DATABASE_DEFAULT,
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		left outer join Manufacturer InsM on InsM.IDMah = ins.IDManufacturer
		left outer join Manufacturer DelM on DelM.IDMah = del.IDManufacturer
		where isnull(ins.IDManufacturer,0) <> isnull(del.IDManufacturer,0) or del.IDPool is null

		) sel 
		
	where isnull(sel.NewValue, '') <> ISNULL(sel.OldValue, '')

		/* DEBUG 
		select ins.*, del.* from inserted ins with (nolock)	
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where ins.[CharSet] <> del.[CharSet] 

		select * from deleted
		--select * from #tmp

		END 
		 DEBUG */


	end try

	begin catch
		-- CREATE ERROR LOG

	end catch
	
END

GO

