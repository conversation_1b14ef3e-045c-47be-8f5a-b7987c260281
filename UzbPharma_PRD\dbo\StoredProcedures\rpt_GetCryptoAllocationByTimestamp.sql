
create PROCEDURE [dbo].[rpt_GetCryptoAllocationByTimestamp]
(
    @FromTimestamp Datetime, @ToTimestamp Datetime
)
AS
BEGIN
	 select   
		  o.IDCustomer,    
		  count(isnull(s.IDOrderCryptoCode,0)) as [Count]   
	from [Order] o with(nolock) 
	left join OrderProduct op with (nolock) on op.IDOrder = o.IDOrder
	left join OrderProductCryptoCode s with (nolock) on s.IDOrderProduct = op.IDOrderProduct
	where o.TimestampCreated between @FromTimestamp and @ToTimestamp
	group by o.IDCustomer   

 
 END

GO

