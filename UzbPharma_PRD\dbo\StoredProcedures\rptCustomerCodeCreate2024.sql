create procedure [dbo].[rptCustomerCodeCreate2024]  
as  
begin  
select c.<PERSON>, count(*) as cnt from BatchEventCode bec with (nolock)  
left join BatchEvent be with (nolock) on bec.IDBatchEvent=be.IDBatchEvent  
left join Batch b with (nolock) on b.IDBatch = be.IDBatch  
left join Product p with (nolock) on p.IDProduct=b.IDProduct  
left join Customer c with (nolock) on c.IDCustomer=p.IDCustomer  
where be.TimestampCreated between '2024-01-01 00:00:00' and '2024-12-31 23:59:59'  
group by c.Name  
order by 2 desc ,1 desc  
end

GO

