CREATE TABLE [dbo].[DocType] (
    [IDDocType]                 INT            IDENTITY (1, 1) NOT NULL,
    [Code]                      NVARCHAR (10)  NOT NULL,
    [Name]                      NVARCHAR (200) NOT NULL,
    [DescriptionRu]             NVARCHAR (512) NULL,
    [DescriptionEn]             NVARCHAR (512) NULL,
    [Assembly]                  NVARCHAR (128) NULL,
    [FQDN]                      NVARCHAR (128) NULL,
    [IsCreateAllowed]           BIT            CONSTRAINT [DF_DocType_IsCreateAllowed] DEFAULT ((0)) NOT NULL,
    [IsReport]                  BIT            CONSTRAINT [DF_DocType_IsReport] DEFAULT ((0)) NOT NULL,
    [PackagingType]             NVARCHAR (50)  NULL,
    [WithSafeStorageLocation]   BIT            CONSTRAINT [DF_DocType_WithSafeStorageLocation] DEFAULT ((0)) NOT NULL,
    [ForSafeStorageLocation]    BIT            CONSTRAINT [DF_DocType_ForSafeStorageLocation] DEFAULT ((0)) NOT NULL,
    [AcceptedRejected]          BIT            CONSTRAINT [DF_DocType_AcceptedRejected] DEFAULT ((0)) NOT NULL,
    [AutodisassemblingSSCC]     BIT            CONSTRAINT [DF_DocType_AutodisassemblingSSCC] DEFAULT ((0)) NOT NULL,
    [Restrictions]              NVARCHAR (MAX) NULL,
    [Note]                      NVARCHAR (500) NULL,
    [XSD]                       NVARCHAR (MAX) NULL,
    [IsOperationAndRestriction] BIT            CONSTRAINT [DF_DocType_IsOperationAndRestriction] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_DocType] PRIMARY KEY CLUSTERED ([IDDocType] ASC)
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [idxByCode_Unique]
    ON [dbo].[DocType]([Code] ASC);


GO

