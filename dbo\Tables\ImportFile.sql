CREATE TABLE [dbo].[ImportFile] (
    [IDImportFile]     INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]       INT            NOT NULL,
    [FileName]         NVARCHAR (256) NOT NULL,
    [Imported]         BIT            CONSTRAINT [DF__ImportFil__IsImp__282DF8C2] DEFAULT ((0)) NOT NULL,
    [IsError]          BIT            CONSTRAINT [DF__ImportFil__IsErr__29221CFB] DEFAULT ((0)) NOT NULL,
    [ImportStatus]     NVARCHAR (MAX) NULL,
    [UserCreated]      NVARCHAR (128) NOT NULL,
    [TimestampCreated] DATETIME       NOT NULL,
    [UserUpdated]      NVARCHAR (128) NULL,
    [TimestampUpdated] DATETIME       NULL,
    CONSTRAINT [PK_ImportFile] PRIMARY KEY CLUSTERED ([IDImportFile] ASC),
    CONSTRAINT [FK_ImportFile_ImportFile] FOREIGN KEY ([IDImportFile]) REFERENCES [dbo].[ImportFile] ([IDImportFile])
);


GO

CREATE NONCLUSTERED INDEX [idxFileName]
    ON [dbo].[ImportFile]([IDCustomer] ASC, [FileName] ASC, [Imported] ASC, [IsError] ASC);


GO

CREATE NONCLUSTERED INDEX [idxIDCustomer]
    ON [dbo].[ImportFile]([IDCustomer] ASC);


GO

