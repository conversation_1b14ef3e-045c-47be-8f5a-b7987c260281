
CREATE procedure [dbo].[spx_UserCreateFromCMD](@LgnName nvarchar(50))
as
begin
	declare @IDService int = (select IDService from MDService WHERE SrcSystem = 'UZ')

	if exists (select top 1 * from MDUserLogin where LgnName = @LgnName)
	begin
		INSERT INTO dbo.UserLogin
			([LgnName]
			,[IDCustomer]
			,[TimeStampCreated]
			,[UserCreated])
		SELECT
			 mdsul.[LgnName]
			,u.[IDCustomer]
			,GETDATE()
			,u.[UserCreated]
		FROM MDUserLogin u 
		inner join MDServiceUserLogin mdsul on u.LgnName = mdsul.LgnName 
			and mdsul.IDService = @IDService
		where u.LgnName = @LgnName
	end

end

GO

