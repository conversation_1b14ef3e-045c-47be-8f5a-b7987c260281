CREATE TABLE [dbo].[BatchEventAggregationItem] (
    [IDBatchEventAggregationItem] BIGINT         IDENTITY (1, 1) NOT NULL,
    [IDBatchEventAggreagtion]     INT            NOT NULL,
    [SerialNumber]                NVARCHAR (128) NULL,
    [TimestampCreated]            DATETIME       NOT NULL,
    [UserCreated]                 NVARCHAR (128) NOT NULL,
    CONSTRAINT [PK_BatchEventAggregationItem] PRIMARY KEY CLUSTERED ([IDBatchEventAggregationItem] ASC),
    CONSTRAINT [FK_BatchEventAggregationItem_BatchEventAggreagtion] FOREIGN KEY ([IDBatchEventAggreagtion]) REFERENCES [dbo].[BatchEventAggreagtion] ([IDBatchEventAggreagtion])
);


GO

CREATE NONCLUSTERED INDEX [idx_BatchEventAggregation]
    ON [dbo].[BatchEventAggregationItem]([IDBatchEventAggreagtion] ASC)
    INCLUDE([SerialNumber]);


GO

