CREATE TABLE [dbo].[PoolInfo] (
    [IDPoolInfo]                  BIGINT         IDENTITY (1, 1) NOT NULL,
    [IDBuffer]                    INT            NOT NULL,
    [Status]                      NVARCHAR (100) NULL,
    [Quantity]                    INT            NULL,
    [LeftInRegistrar]             INT            NULL,
    [RegistrarId]                 NVARCHAR (200) NULL,
    [IsRegistrarReady]            BIT            NULL,
    [RegistrarErrorCount]         INT            NULL,
    [LastRegistrarErrorTimestamp] BIGINT         NULL,
    [RejectionReason]             NVARCHAR (300) NULL,
    [TimestampCreated]            DATETIME       CONSTRAINT [DF_PoolInfo_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]                 NVARCHAR (128) NOT NULL,
    CONSTRAINT [PK_PoolInfo] PRIMARY KEY CLUSTERED ([IDPoolInfo] ASC),
    CONSTRAINT [FK_PoolInfo_Buffer] FOREIGN KEY ([IDBuffer]) REFERENCES [dbo].[Buffer] ([IDBuffer])
);


GO

