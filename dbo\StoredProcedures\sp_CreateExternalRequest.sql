CREATE PROCEDURE [dbo].[sp_CreateExternalRequest] (@ReferenceID nvarchar(50), @IDPool int, @RequestedQnt int, @LgnName nvarchar(128))
AS
BEGIN
	SET NOCOUNT ON;

	insert into Request (ReferenceID, IDPool, RequestedQty, AllocatedQty, Ready, TimestampCreated, UserCreated )  
	values (@ReferenceID, @IDPool, @RequestedQnt, 0, 0, GETDATE(), @LgnName)  

	select CAST(SCOPE_IDENTITY() as int) as Result
END

GO

