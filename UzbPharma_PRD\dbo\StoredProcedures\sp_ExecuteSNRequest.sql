CREATE procedure [dbo].[sp_ExecuteSNRequest](
  @IDOrderProductPoolRequest int,
  @IDOrderProduct int, 
  @IDPool int,
  @IDOrder int, 
  @RequestedQnt int,
  @ReferenceID nvarchar(50), 
  @LgnName nvarchar(128),
  @IDRequest int output,
  @AllocQty int output)  
as  
BEGIN
  
--SET NOCOUNT ON  
	BEGIN TRY  
  
	 BEGIN TRANSACTION   
		declare @Serials table (SN nvarchar(20));
		declare @ParamDef nvarchar(max) = 
	N'@pReferenceID nvarchar(50),
	@pIDPool int,
	@pIDOrder int,
	@pRequestedQnt int,
	@pLgnName nvarchar(128),
	@pIDRequest int OUTPUT, 
	@pAllocQty int OUTPUT';
		-- This is executed as dynamic SQL to break up Circular Database Reference
		declare @SQL nvarchar(max) = 
		N'exec [SGCloudSNGenerator_PRD].[dbo].[sp_CreateExternalRequest2] 
		  @pReferenceID, @pIDPool, @pIDOrder, @pRequestedQnt, @pLgnName, @pIDRequest output, @pAllocQty output;';

		insert into @serials
		exec sp_executesql @SQL, @ParamDef, 
		   @pReferenceID = @ReferenceID,
		   @pIDPool = @IDPool,
		   @pIDOrder = @IDOrder,
		   @pRequestedQnt = @RequestedQnt,
		   @pLgnName = @LgnName,
		   @pIDRequest = @IDRequest output, 
		   @pAllocQty = @AllocQty output;
  
		select * From @Serials;

	  insert into Serial (IDOrderProductPoolRequest, IDOrderProduct, SerialNumber, TimestampCreated, UserCreated)  
	  select @IDOrderProductPoolRequest, @IDOrderProduct, s.SN, GETDATE(), @LgnName  
	  from @Serials S
  

	  if @@ROWCOUNT <> @AllocQty 
		ROLLBACK TRANSACTION;
	  ELSE
		COMMIT TRANSACTION   
 
   
	END TRY  
  
	BEGIN CATCH  
  
	 ROLLBACK TRANSACTION  
  
	  --SELECT ERROR_NUMBER() AS ErrorNumber, ERROR_MESSAGE() AS ErrorMessage;

	END CATCH  

	BEGIN TRANSACTION Tran1
	
		BEGIN TRY
		
			update OrderProduct 
     	    set AllocatedSerials =  (select cnt = count(*) from serial with (nolock) where IDOrderProduct = @IDOrderProduct) 
			where IDOrderProduct = @IDOrderProduct;
	       
			update OrderProductPoolRequest 
			set AllocatedQuantity =  (select cnt = count(*) from serial with (nolock) where IDOrderProductPoolRequest = @IDOrderProductPoolRequest)
			where IDOrderProductPoolRequest = @IDOrderProductPoolRequest
	
			COMMIT TRANSACTION [Tran1]
	
		END TRY
		BEGIN CATCH
		
			ROLLBACK TRANSACTION [Tran1]
	
		END CATCH
	
  
END

GO

