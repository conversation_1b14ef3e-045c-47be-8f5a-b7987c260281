CREATE procedure [dbo].[spx_UserUpdateFromCMD](@LgnName nvarchar(50))
as
begin

	declare @IDService int = (select IDService from MDService WHERE SrcSystem = 'SN')

	if exists (select top 1 * from MDUserLogin where LgnName = @LgnName)
	begin
		update u
		set IDManufacturer = mdu.IDManufacturer,
			ExternalReceiverCode = mdsul.ExternalRecieverCode,
			ExternalSenderCode = mdsul.ExternalSenderCode
		from 
			dbo.UserLogin u
			left join MDUserLogin mdu on mdu.LgnName = u.LgnName collate database_default
			left join MDServiceUserLogin mdsul on mdu.LgnName = mdsul.LgnName collate database_default
			and mdsul.IDService = @IDService
		where 
			u.LgnName = @LgnName
	end
end

GO

