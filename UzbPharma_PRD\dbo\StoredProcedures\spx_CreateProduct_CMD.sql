CREATE PROCEDURE [dbo].[spx_CreateProduct_CMD](
	@IDCustomer INT, 
	@CodeType nvarchar(10), 
	@Code nvarchar(14), 
	@Name nvarchar(255), 
	@CommonName nvarchar(255),
	@Form nvarchar(100), 
	@PackType nvarchar(50), 
	@PackSize int, 
	@Strength nvarchar(100), 
	@UserCreated nvarchar(128), 
	@IsActive bit, 
	@SerialNumberType nvarchar(50),
	@IDProduct int out)
AS BEGIN

	DECLARE @IDServiceMasterRecordUZ INT = (SELECT TOP(1) IDService FROM MDService
										    WHERE SrcSystem = 'UZ');

	DECLARE @SerializationTypeUZ NVARCHAR(128) = (SELECT TOP(1) SerializationType FROM MDService
											WHERE SrcSystem = 'UZ');

	if NOT EXISTS (select top 1 * from MDProduct 
		where IDCustomer = @IDCustomer and IDServiceMasterRecord = @IDServiceMasterRecordUZ
			  and CodeType = @CodeType and Code = @Code)
	begin
		INSERT INTO MDProduct
			   ([IDCustomer]
			   ,[CodeType]
			   ,[Code]
			   ,[Name]
			   ,[CommonName]
			   ,[Form]
			   ,[Strength]
			   ,[PackSize]
			   ,[PackType]
			   ,[TimeStampCreated]
			   ,[UserCreated]
			   ,[IsActive]
			   ,[IDServiceMasterRecord]
			   ,[SerialNumberSourceType]
			   ,[SerializationType])
		values
		(@IDCustomer, @CodeType, @Code, @Name, @CommonName, @Form
		, @Strength, @PackSize, @PackType, GETDATE(), @UserCreated, @IsActive
		,@IDServiceMasterRecordUZ, @SerialNumberType, @SerializationTypeUZ)

		SET @IDProduct = @@IDENTITY

		-- insert into local product table
		set identity_insert dbo.product on
		insert into dbo.Product(IDProduct
								,IDCustomer
								,CodeType
								,Code
								,[Name]
								,TimeStampCreated
								,UserCreated
								,SerialNumberType
								,IsActive)
			values
								(@IDProduct
								,@IDCustomer
								,@CodeType
								,@Code
								,@Name
								,GETDATE()
								,@UserCreated
								,'SELF_MADE'
								,0)
		set identity_insert dbo.product off

	end
	else
	begin
		set @IDProduct = (select top 1 IDProduct from MDProduct 
			where IDCustomer = @IDCustomer and IDServiceMasterRecord = @IDServiceMasterRecordUZ 
			and CodeType = @CodeType and Code = @Code)
		
		set identity_insert dbo.Product on
		insert into dbo.Product(IDProduct
								,IDCustomer
								,CodeType
								,Code
								,[Name]
								,TimeStampCreated
								,UserCreated
								,SerialNumberType
								,IsActive)
			select IDProduct
				  ,IDCustomer
				  ,CodeType
				  ,Code
				  ,[Name]
				  ,TimeStampCreated
				  ,UserCreated
				  ,SerialNumberSourceType
				  ,IsActive
			from MDProduct with (nolock)
			where IDProduct = @IDProduct
		set identity_insert dbo.Product off

	end
	

END

GO

