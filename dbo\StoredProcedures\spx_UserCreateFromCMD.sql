CREATE procedure [dbo].[spx_UserCreateFromCMD](@LgnName nvarchar(50))  
as  
begin  
 declare @IDService int = (select IDService from MDService WHERE SrcSystem = 'SN')  
  
 if exists (select top 1 * from MDUserLogin where LgnName = @LgnName)  
 begin  
  INSERT INTO dbo.UserLogin  
   ([LgnName]  
   ,[IDCustomer]  
   ,[IDManufacturer]  
   ,[TimeStampCreated]  
   ,[UserCreated]  
   ,ExternalSenderCode  
   ,ExternalReceiverCode)  
  SELECT  
    mdsul.[LgnName]  
   ,mdu.[IDCustomer]  
   ,mdu.IDManufacturer  
   ,GETDATE()  
   ,mdu.[UserCreated]  
   ,mdsul.ExternalSenderCode  
   ,mdsul.ExternalRecieverCode  
  FROM MDUserLogin mdu 
  inner join MDServiceUserLogin mdsul on mdu.LgnName = mdsul.LgnName collate database_default  
   and mdsul.IDService = @IDService 
  WHERE mdu.LgnName = @LgnName  

 end  
  
end

GO

