create procedure [dbo].[spx_CustomerUpdateFromCMD](@IDCustomer int)
as
begin
	if exists (select top 1 * from MDCustomer with (nolock) where IDCustomer = @IDCustomer)
	begin
		update c
		set [Name] = mdc.[Name],
		    UserUpdated = mdc.UserUpdated,
			TimestampUpdated = GETDATE()
		from 
			dbo.Customer c
			inner join MDCustomer mdc on mdc.IDCustomer = c.IDCustomer  
		where 
			c.IDCustomer = @IDCustomer

	end
	

	
end

GO

