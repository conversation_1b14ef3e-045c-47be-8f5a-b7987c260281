CREATE TABLE [dbo].[Buffer] (
    [<PERSON><PERSON>uffer]         INT            IDENTITY (1, 1) NOT NULL,
    [IDOrderInfo]      INT            NOT NULL,
    [LeftInBuffer]     INT            NULL,
    [PoolsExhausted]   BIT            NULL,
    [TotalCodes]       INT            NULL,
    [UnavailableCodes] INT            NULL,
    [AvailableCodes]   INT            NULL,
    [OrderId]          NVARCHAR (200) NULL,
    [GTIN]             NVARCHAR (14)  NULL,
    [BufferStatus]     NVARCHAR (128) NULL,
    [TotalPassed]      INT            NULL,
    [RejectionReason]  NVARCHAR (300) NULL,
    [TimestampCreated] DATETIME       CONSTRAINT [D<PERSON>_<PERSON><PERSON><PERSON>_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]      NVARCHAR (128) NOT NULL,
    CONSTRAINT [PK_Buffer] PRIMARY KEY CLUSTERED ([IDBuffer] ASC),
    CONSTRAINT [FK_Buffer_OrderInfo] FOREIGN KEY ([IDOrderInfo]) REFERENCES [dbo].[OrderInfo] ([IDOrderInfo])
);


GO

