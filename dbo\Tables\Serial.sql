CREATE TABLE [dbo].[Serial] (
    [IDSerial]         BIGINT          IDENTITY (1, 1) NOT NULL,
    [IDRequest]        INT             NOT NULL,
    [IDProduct]        INT             NOT NULL,
    [IDPool]           INT             NOT NULL,
    [SerialNumber]     NVARCHAR (20)   COLLATE Cyrillic_General_CS_AS NOT NULL,
    [IsActive]         BIT             CONSTRAINT [DF_Serial_IsActive] DEFAULT ((0)) NOT NULL,
    [State]            NVARCHAR (50)   NULL,
    [TimestampCreated] DATETIME        NOT NULL,
    [UserCreated]      NVARCHAR (128)  NOT NULL,
    [TimestampUpdated] DATETIME        NULL,
    [UserUpdated]      NVARCHAR (128)  NULL,
    [Codes]            NVARCHAR (3000) NULL,
    [IDOrderRequest]   INT             NULL,
    [AI91]             NVARCHAR (100)  NULL,
    [AI92]             NVARCHAR (100)  NULL,
    CONSTRAINT [PK_Serial] PRIMARY KEY NONCLUSTERED ([IDSerial] ASC) ON [PRIMARY]
) ON [SerialRangePartionScheme] ([IDPool]);


GO

CREATE NONCLUSTERED INDEX [idx_IDRequest_IDPool_IsActive]
    ON [dbo].[Serial]([IDRequest] ASC, [IDPool] ASC, [IsActive] ASC)
    ON [SerialRangePartionScheme] ([IDPool]);


GO

CREATE NONCLUSTERED INDEX [idx_IDRequest_IDPool_IDOrderRequest]
    ON [dbo].[Serial]([IDRequest] ASC, [IDPool] ASC, [IDOrderRequest] ASC)
    ON [SerialRangePartionScheme] ([IDPool]);


GO

CREATE NONCLUSTERED INDEX [idxIDRequest]
    ON [dbo].[Serial]([IDRequest] ASC)
    INCLUDE([SerialNumber]) WITH (FILLFACTOR = 95)
    ON [SerialRangePartionScheme] ([IDPool]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [idx_IDProduct_Serial]
    ON [dbo].[Serial]([IDProduct] ASC, [SerialNumber] ASC)
    ON [PRIMARY];


GO

CREATE NONCLUSTERED INDEX [idx_IDPool_IDRequest]
    ON [dbo].[Serial]([IDPool] ASC, [IDRequest] ASC, [IDSerial] ASC)
    ON [SerialRangePartionScheme] ([IDPool]);


GO

CREATE NONCLUSTERED INDEX [idx_IDOrderRequest_Serial]
    ON [dbo].[Serial]([IDOrderRequest] ASC, [SerialNumber] ASC)
    ON [SerialRangePartionScheme] ([IDPool]);


GO

