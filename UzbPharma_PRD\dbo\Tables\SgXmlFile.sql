CREATE TABLE [dbo].[SgXmlFile] (
    [IDSgXmlFile]      INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]       INT            NOT NULL,
    [FileName]         NVARCHAR (512) NOT NULL,
    [ProductCode]      NVARCHAR (50)  NULL,
    [ProductName]      NVARCHAR (500) NULL,
    [BatchID]          NVARCHAR (50)  NULL,
    [ExpiryDate]       DATETIME       NULL,
    [Data]             NVARCHAR (MAX) NULL,
    [Description]      NVARCHAR (500) NULL,
    [TimestampCreated] DATETIME       NOT NULL,
    [UserCreated]      NVARCHAR (128) NOT NULL,
    [IDBatch]          INT            NULL,
    CONSTRAINT [PK_SgXmlFile] PRIMARY KEY CLUSTERED ([IDSgXmlFile] ASC),
    CONSTRAINT [FK_SgXmlFile_Customer] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE
);


GO

CREATE NONCLUSTERED INDEX [idxByProduct]
    ON [dbo].[SgXmlFile]([ProductCode] ASC, [ProductName] ASC, [BatchID] ASC);


GO

