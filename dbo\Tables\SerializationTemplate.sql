CREATE TABLE [dbo].[SerializationTemplate] (
    [IDSerializationTemplate] INT            IDENTITY (1, 1) NOT NULL,
    [SerializationType]       NVARCHAR (20)  NOT NULL,
    [CharSet]                 NVARCHAR (100) NOT NULL,
    [Length]                  INT            NOT NULL,
    [Step]                    INT            NULL,
    [Count]                   INT            NOT NULL,
    [Threshold]               INT            NULL,
    [IDCustomer]              INT            NOT NULL,
    [Name]                    NVARCHAR (128) NULL,
    [UserCreated]             NVARCHAR (128) NULL,
    [TimestampCreated]        DATETIME       CONSTRAINT [DF_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserUpdated]             NVARCHAR (128) NULL,
    [TimestampUpdated]        DATETIME       NULL,
    [IsActive]                BIT            CONSTRAINT [DF_SerializationTemplate_IsActive] DEFAULT ((1)) NOT NULL,
    CONSTRAINT [PK_SerializationTemplates] PRIMARY KEY CLUSTERED ([IDSerializationTemplate] ASC) WITH (FILLFACTOR = 95)
);


GO

--ALTER TABLE [SGCloudSNGenerator_Test].[dbo].[SerializationTemplate] ADD [IsActive] BIT
--GO

CREATE TRIGGER [dbo].[SerializationTemplateAudit]
   ON dbo.SerializationTemplate
   AFTER INSERT,DELETE,UPDATE
AS 
BEGIN
	SET NOCOUNT ON;

	begin try
		-- process inserted records

	insert into dbo.AuditTrail([Action], [IDSerializationTemplate], [TableName], [FieldName], [OldValue], [NewValue], [Timestamp], [User])
	select * from (

		select 
			[Action] = case when del.IDSerializationTemplate is null then 'Create' else 'Update' end,
			[IDSerializationTemplate] = ins.IDSerializationTemplate,
			[TableName] = 'dbo.SerializationTemplate',
			[FieldName] = 'SerializationType',
			OldValue = cast(del.SerializationType as nvarchar(max)),
			NewValue = cast(ins.SerializationType as nvarchar(max)),
			[Timestamp] = coalesce(ins.TimeStampUpdated, ins.TimeStampCreated),
			[User] =coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDSerializationTemplate = ins.IDSerializationTemplate
		where isnull(ins.SerializationType,'') <> isnull(del.SerializationType,'') or del.IDSerializationTemplate is null
		
		union all 

		select 
			[Action] = case when del.IDSerializationTemplate is null then 'Create' else 'Update' end,
			[IDSerializationTemplate] = ins.IDSerializationTemplate,
			[TableName] = 'dbo.SerializationTemplate',
			[FieldName] = 'CharSet',
			OldValue = cast(del.CharSet as nvarchar(max)),
			NewValue = cast(ins.CharSet as nvarchar(max)),
			[Timestamp] = coalesce(ins.TimeStampUpdated, ins.TimeStampCreated),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDSerializationTemplate = ins.IDSerializationTemplate
		where isnull(ins.CharSet,'') <> isnull(del.CharSet,'') or del.IDSerializationTemplate is null

		union all 

		select 
			[Action] = case when del.IDSerializationTemplate is null then 'Create' else 'Update' end,
			[IDSerializationTemplate] = ins.IDSerializationTemplate,
			[TableName] = 'dbo.SerializationTemplate',
			[FieldName] = 'Length',
			OldValue = cast(del.[Length] as nvarchar(max)),
			NewValue = cast(ins.[Length] as nvarchar(max)),
			[Timestamp] = coalesce(ins.TimeStampUpdated, ins.TimeStampCreated),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDSerializationTemplate = ins.IDSerializationTemplate
		where isnull(ins.[Length],'') <> isnull(del.[Length],'') or del.IDSerializationTemplate is null
		
		union all 

		select 
			[Action] = case when del.IDSerializationTemplate is null then 'Create' else 'Update' end,
			[IDSerializationTemplate] = ins.IDSerializationTemplate,
			[TableName] = 'dbo.SerializationTemplate',
			[FieldName] = 'Step',
			OldValue = cast(del.Step as nvarchar(max)),
			NewValue = cast(ins.Step as nvarchar(max)),
			[Timestamp] = coalesce(ins.TimeStampUpdated, ins.TimeStampCreated),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDSerializationTemplate = ins.IDSerializationTemplate
		where isnull(ins.Step,'') <> isnull(del.Step,'') or del.IDSerializationTemplate is null

		union all 

		select 
			[Action] = case when del.IDSerializationTemplate is null then 'Create' else 'Update' end,
			[IDSerializationTemplate] = ins.IDSerializationTemplate,
			[TableName] = 'dbo.SerializationTemplate',
			[FieldName] = 'Count',
			OldValue = cast(del.[Count] as nvarchar(max)),
			NewValue = cast(ins.[Count] as nvarchar(max)),
			[Timestamp] = coalesce(ins.TimeStampUpdated, ins.TimeStampCreated),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDSerializationTemplate = ins.IDSerializationTemplate
		where isnull(ins.[Count],'') <> isnull(del.[Count],'') or del.IDSerializationTemplate is null

		union all 

		select 
			[Action] = case when del.IDSerializationTemplate is null then 'Create' else 'Update' end,
			[IDSerializationTemplate] = ins.IDSerializationTemplate,
			[TableName] = N'dbo.SerializationTemplate',
			[FieldName] = N'Threshold',
			OldValue = cast(del.[Threshold] as nvarchar(max)),
			NewValue = cast(ins.[Threshold] as nvarchar(max)),
			[Timestamp] = coalesce(ins.TimeStampUpdated, ins.TimeStampCreated),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDSerializationTemplate = ins.IDSerializationTemplate
		where isnull(ins.[Threshold],'') <> isnull(del.[Threshold],'') or del.IDSerializationTemplate is null

		union all 

		select 
			[Action] = case when del.IDSerializationTemplate is null then 'Create' else 'Update' end,
			[IDSerializationTemplate] = ins.IDSerializationTemplate,
			[TableName] = N'dbo.SerializationTemplate',
			[FieldName] = N'IDCustomer',
			OldValue = Ddel.[Name] COLLATE DATABASE_DEFAULT,
			NewValue = Cins.[Name] COLLATE DATABASE_DEFAULT,
			[Timestamp] = coalesce(ins.TimeStampUpdated, ins.TimeStampCreated),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDSerializationTemplate = ins.IDSerializationTemplate
		left join Customer Cins on Cins.IDCustomer = ins.IDCustomer
		left join Customer Ddel on Ddel.IDCustomer = del.IDCustomer
		where isnull(ins.[IDCustomer],'') <> isnull(del.[IDCustomer],'') or del.IDSerializationTemplate is null

		union all 

		select 
			[Action] = case when del.IDSerializationTemplate is null then N'Create' else N'Update' end,
			[IDSerializationTemplate] = ins.IDSerializationTemplate,
			[TableName] = N'dbo.SerializationTemplate',
			[FieldName] = N'IsActive',
			OldValue = cast(case del.IsActive when 0 then N'NO' when 1 then N'YES' else null end as nvarchar(max)),
			NewValue = cast(case ins.IsActive when 0 then N'NO' when 1 then N'YES' else null end as nvarchar(max)),
			[Timestamp] = coalesce(ins.TimeStampUpdated, ins.TimeStampCreated),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDSerializationTemplate = ins.IDSerializationTemplate
		where isnull(ins.IsActive, 0) <> isnull(del.IsActive, 0) or del.IDSerializationTemplate is null

		union all 


		select 
			[Action] = case when del.IDSerializationTemplate is null then N'Create' else N'Update' end,
			[IDSerializationTemplate] = ins.IDSerializationTemplate,
			[TableName] = N'dbo.SerializationTemplate',
			[FieldName] = N'Name',
			OldValue = cast(del.[Name] as nvarchar(max)),
			NewValue = cast(ins.[Name] as nvarchar(max)),
			[Timestamp] = coalesce(ins.TimeStampUpdated, ins.TimeStampCreated),
			[User] = coalesce(ins.UserUpdated, ins.UserCreated)
		from inserted ins with (nolock)
		left join deleted del with (nolock) on del.IDSerializationTemplate = ins.IDSerializationTemplate
		where isnull(ins.[Name],'') <> isnull(del.[Name],'') or del.IDSerializationTemplate is null
		
		
		union all 

		select 
			[Action] = 'Delete',
			[IDSerializationTemplate] = del.IDSerializationTemplate,
			[TableName] = N'dbo.SerializationTemplate',
			[FieldName] = N'',
			OldValue = N'',
			NewValue = N'Record deleted',
			[Timestamp] = GETDATE(),
			[User] = del.UserUpdated
		from deleted del with (nolock)
		left join inserted ins with (nolock) on del.IDSerializationTemplate = ins.IDSerializationTemplate
		where ins.IDSerializationTemplate is null

		) sel 
		
	where isnull(sel.NewValue, N'') <> ISNULL(sel.OldValue, N'')

		/* DEBUG 
		select ins.*, del.* from inserted ins with (nolock)	
		left join deleted del with (nolock) on del.IDPool = ins.IDPool
		where ins.[CharSet] <> del.[CharSet] 

		select * from deleted
		--select * from #tmp

		END 
		 DEBUG */


	end try

	begin catch
		-- CREATE ERROR LOG

	end catch
	
END

GO

