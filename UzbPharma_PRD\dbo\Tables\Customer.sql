CREATE TABLE [dbo].[Customer] (
    [IDCustomer]            INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomerType]        SMALLINT       NULL,
    [Name]                  NVARCHAR (255) NULL,
    [OmsID]                 NVARCHAR (50)  NULL,
    [Username]              NVARCHAR (128) NULL,
    [Password]              NVARCHAR (100) NULL,
    [OmsUrl]                NVARCHAR (200) NULL,
    [OmsToken]              NVARCHAR (50)  NULL,
    [MdlpUserID]            NVARCHAR (50)  NULL,
    [MdlpPassword]          NVARCHAR (100) NULL,
    [ClientID]              NVARCHAR (50)  NULL,
    [ClientSecret]          NVARCHAR (50)  NULL,
    [MdlpUrl]               NVARCHAR (200) NULL,
    [MdlpToken]             NVARCHAR (50)  NULL,
    [MdlpTokenExpiryDate]   DATETIME       NULL,
    [MdlpMahID]             NVARCHAR (50)  NULL,
    [CertificateThumbprint] NVARCHAR (100) NULL,
    [TimestampCreated]      DATETIME       CONSTRAINT [DF_Customer_TimeStampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]           NVARCHAR (128) CONSTRAINT [DF_Customer_UserCreated] DEFAULT (suser_sname()) NOT NULL,
    [TimestampUpdated]      DATETIME       NULL,
    [UserUpdated]           NVARCHAR (128) NULL,
    [MdlpPlaceID]           NVARCHAR (50)  NULL,
    [OmsConnectionId]       NVARCHAR (50)  NULL,
    [OmsTokenExpiry]        DATETIME       NULL,
    CONSTRAINT [PK_Customer] PRIMARY KEY CLUSTERED ([IDCustomer] ASC),
    CONSTRAINT [FK_Customer_CustomerType] FOREIGN KEY ([IDCustomerType]) REFERENCES [dbo].[CustomerType] ([IDCustomerType]) ON UPDATE CASCADE
);


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'OMS ID for orders', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Customer', @level2type = N'COLUMN', @level2name = N'OmsID';


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'MDLP Token is issued for 30 minutes, and must not be renewd before expiration time ', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Customer', @level2type = N'COLUMN', @level2name = N'MdlpToken';


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'OMS Url', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Customer', @level2type = N'COLUMN', @level2name = N'OmsUrl';


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'MDLP token expiration time, normally 30 minutes from token request.', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Customer', @level2type = N'COLUMN', @level2name = N'MdlpTokenExpiryDate';


GO

EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'OMS Token - one for all users', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'Customer', @level2type = N'COLUMN', @level2name = N'OmsToken';


GO

