CREATE TABLE [dbo].[Product] (
    [IDProduct]        INT            IDENTITY (1, 1) NOT NULL,
    [IDCustomer]       INT            NOT NULL,
    [CodeType]         NVARCHAR (10)  NOT NULL,
    [Code]             NVARCHAR (14)  NOT NULL,
    [Name]             NVARCHAR (255) NOT NULL,
    [CommonName]       NVARCHAR (255) NULL,
    [Form]             NVARCHAR (100) NULL,
    [Strength]         NVARCHAR (100) NULL,
    [PackSize]         INT            NULL,
    [PackType]         NVARCHAR (50)  NULL,
    [RegDate]          NVARCHAR (30)  NULL,
    [RegHolder]        NVARCHAR (200) NULL,
    [RegNumber]        NVARCHAR (50)  NULL,
    [DrugCode]         NVARCHAR (255) NULL,
    [TimestampCreated] DATETIME       CONSTRAINT [DF_Product_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]      NVARCHAR (128) CONSTRAINT [DF_Product_UserCreated] DEFAULT (user_name()) NOT NULL,
    [TimestampUpdated] DATETIME       NULL,
    [UserUpdated]      NVARCHAR (128) NULL,
    [SerialNumberType] NVARCHAR (50)  CONSTRAINT [DF_Product_SerialNumberType] DEFAULT ('SELF_MADE') NOT NULL,
    [IsActive]         BIT            CONSTRAINT [DF_Product_IsActive] DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_Product] PRIMARY KEY CLUSTERED ([IDProduct] ASC),
    CONSTRAINT [FK_Customer_Product] FOREIGN KEY ([IDCustomer]) REFERENCES [dbo].[Customer] ([IDCustomer]) ON UPDATE CASCADE
);


GO

