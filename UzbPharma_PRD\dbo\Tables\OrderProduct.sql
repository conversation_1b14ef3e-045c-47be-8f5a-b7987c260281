CREATE TABLE [dbo].[OrderProduct] (
    [IDOrderProduct]   INT            IDENTITY (1, 1) NOT NULL,
    [IDOrder]          INT            NOT NULL,
    [IDProduct]        INT            NOT NULL,
    [TimestampCreated] DATETIME       CONSTRAINT [DF_OrderProduct_TimestampCreated] DEFAULT (getdate()) NOT NULL,
    [UserCreated]      NVARCHAR (128) NOT NULL,
    [Quantity]         INT            NULL,
    [AllocatedSerials] INT            NOT NULL,
    [TemplateID]       INT            NULL,
    [GTIN]             NVARCHAR (14)  NULL,
    CONSTRAINT [PK_OrderProduct] PRIMARY KEY CLUSTERED ([IDOrderProduct] ASC),
    CONSTRAINT [FK_OrderProduct_Order] FOREIGN KEY ([IDOrder]) REFERENCES [dbo].[Order] ([IDOrder]),
    CONSTRAINT [FK_OrderProduct_Product] FOREIGN KEY ([IDProduct]) REFERENCES [dbo].[Product] ([IDProduct]) ON UPDATE CASCADE
);


GO

